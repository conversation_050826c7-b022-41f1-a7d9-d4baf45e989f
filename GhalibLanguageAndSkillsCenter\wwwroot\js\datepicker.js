window.replaceMonthNames = function () {
    const dariMonths = [
        "حمل", "ثور", "جوزا", "سرطان",
        "اسد", "سنبله", "میزان", "عقرب",
        "قوس", "جدی", "دلو", "حوت"
    ];

    const observer = new MutationObserver(() => {
        document.querySelectorAll(".calendar-header .month-name").forEach((el) => {
            let original = el.textContent.trim();
            const iranianMonths = [
                "فروردین", "اردیبهشت", "خرداد", "تیر",
                "مرداد", "شهریور", "مهر", "آبان",
                "آذر", "دی", "بهمن", "اسفند"
            ];

            const index = iranianMonths.indexOf(original);
            if (index !== -1) {
                el.textContent = dariMonths[index];
            }
        });
    });

    // Watch the entire calendar for changes
    const calendarPopup = document.querySelector(".calendar-container") || document.body;
    observer.observe(calendarPopup, { childList: true, subtree: true });
};
