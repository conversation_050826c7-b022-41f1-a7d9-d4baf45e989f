﻿using Dapper;

namespace GhalibLanguageAndSkillsCenter.Services
{
    public interface IGenericRepository<T>
    {
        Task<IEnumerable<T>> GetAllAsync(string storedProcedure);
        Task<T> GetByIdAsync(string storedProcedure, object parameters);
        Task AddAsync(string storedProcedure, object parameters);
        Task UpdateAsync(string storedProcedure, object parameters);
        Task DeleteAsync(string storedProcedure, object parameters);
        Task<IEnumerable<T>> GetAllAsyncById(string storedProcedure,object parameters);
        Task<DynamicParameters> AddAsyncAndGetOutpot(string storedProcedure, object parameters);


    }

}
