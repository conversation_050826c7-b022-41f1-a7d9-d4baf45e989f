﻿@page "/Account/Login"
@layout GhalibLanguageAndSkillsCenter.Components.Layout.LoginAndRegisterLayout

@using System.ComponentModel.DataAnnotations
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Identity
@using GhalibLanguageAndSkillsCenter.Data

@inject SignInManager<ApplicationUser> SignInManager
@inject ILogger<Login> Logger
@inject NavigationManager NavigationManager
@inject IdentityRedirectManager RedirectManager

<PageTitle>Log in</PageTitle>

<link href="css/auth.css" rel="stylesheet" />

<div class="auth-container">
    <div class="auth-card">
        <h1 class="auth-header">ورود به حساب کاربری</h1>
        <StatusMessage Message="@errorMessage" />

        <EditForm Model="Input" OnValidSubmit="LoginUser" FormName="login">
            <DataAnnotationsValidator />
            <ValidationSummary class="auth-validation" />

            <div class="auth-form-group">
                <InputText @bind-Value="Input.Email"
                           id="Input.Email"
                           class="auth-input"
                           placeholder="ایمیل"
                           autocomplete="username" />
                <ValidationMessage For="() => Input.Email" class="auth-validation" />
            </div>

            <div class="auth-form-group">
                <InputText @bind-Value="Input.Password"
                           type="password"
                           id="Input.Password"
                           class="auth-input"
                           placeholder="password"
                           autocomplete="current-password" />
                <ValidationMessage For="() => Input.Password" class="auth-validation" />
            </div>

            <div class="form-check auth-form-group--checkbox">
                <InputCheckbox @bind-Value="Input.RememberMe"
                               id="RememberMe"
                               class="form-check-input darker-border-checkbox" />
                <label for="RememberMe" class="form-check-label">
                   مرا بخاطر بسپارید
                </label>
            </div>

            <button type="submit" class="auth-btn">ورود</button>

            <div class="auth-links">
             
                |
             
                |
               
            </div>
        </EditForm>
    </div>
</div>

@code {
    private string? errorMessage;

    [CascadingParameter] private HttpContext HttpContext { get; set; } = default!;
    [SupplyParameterFromForm] private InputModel Input { get; set; } = new();
    [SupplyParameterFromQuery] private string? ReturnUrl { get; set; }

    protected override async Task OnInitializedAsync()
    {
        if (HttpMethods.IsGet(HttpContext.Request.Method))
            await HttpContext.SignOutAsync(IdentityConstants.ExternalScheme);
    }

    public async Task LoginUser()
    {
        var result = await SignInManager.PasswordSignInAsync(
          Input.Email, Input.Password, Input.RememberMe, lockoutOnFailure: false);

        if (result.Succeeded)
        {
            Logger.LogInformation("User logged in.");
            RedirectManager.RedirectTo(ReturnUrl);
        }
        else if (result.RequiresTwoFactor)
        {
            RedirectManager.RedirectTo("Account/LoginWith2fa", new()
                {
                    ["returnUrl"] = ReturnUrl,
                    ["rememberMe"] = Input.RememberMe
                });
        }
        else if (result.IsLockedOut)
        {
            RedirectManager.RedirectTo("Account/Lockout");
        }
        else
        {
            errorMessage = "Error: Invalid login attempt.";
        }
    }

    private sealed class InputModel
    {
        [Required, EmailAddress] public string Email { get; set; } = "";
        [Required, DataType(DataType.Password)] public string Password { get; set; } = "";
        [Display(Name = "Remember me?")] public bool RememberMe { get; set; }
    }
}
