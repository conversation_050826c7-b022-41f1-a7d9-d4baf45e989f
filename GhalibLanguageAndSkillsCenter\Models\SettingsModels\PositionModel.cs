﻿using System.ComponentModel.DataAnnotations;

namespace GhalibLanguageAndSkillsCenter.Models.SettingsModels
{
    public class PositionModel
    {
       public int PositionId { get; set; }
        [Required(ErrorMessage = "Please enter  EnglishName.")]
        public string EnglishName { get; set; }
        [Required(ErrorMessage = "Please enter  DariName.")]

        public string DariName { get; set; }
        [Required(ErrorMessage = "Please enter PashtoName.")]

        public string PashtoName { get; set; }


    }
}
