﻿@using GhalibLanguageAndSkillsCenter.Models.Payment
@inject IGenericRepository<StudentPaymentModel> historyRepository
@inject NavigationManager Nav
@rendermode InteractiveServer

<div class="shadow-lg p-4 mt-4">
    @if (history == null)
    {
        <p>Loading…</p>
    }
    else if (!history.Any())
    {
        <p><em>No payment history found.</em></p>
    }
    else
    {
        <!-- ── ROW: table on the left (8 cols), profile on the right (4 cols) -->
        <div class="row">
            <!-- RIGHT: Profile Card (image full-width, colored name bar underneath) -->
            <div class="col-lg-4 d-flex justify-content-center">
                <div class="card shadow-sm border-0 mb-3" style="width: 100%; max-width: 320px; border-radius: 16px;">
                    @if (history.First().ProfileImage != null && history.First().ProfileImage.Length > 0)
                    {
                        <!-- Profile Image with rounded top corners -->
                        var base64Image = $"data:image/jpeg;base64,{Convert.ToBase64String(history.First().ProfileImage)}";

                        <img src="@base64Image"
                             class="card-img-top"
                             alt="Student Profile"
                             style="border-top-left-radius: 16px; border-top-right-radius: 16px; object-fit: cover; height: 220px;" />
                    }
                    else
                    {
                        <!-- Placeholder -->
                        <div class="bg-secondary d-flex align-items-center justify-content-center"
                             style="height: 220px; border-top-left-radius: 16px; border-top-right-radius: 16px; color: white;">
                            <span>No Profile</span>
                        </div>
                    }

                    <div class="card-body text-center p-3">
                        <h5 class="text-primary fw-bold mb-1">@history.First().StudentName</h5>  
                    </div>

                 
                </div>
            </div>



            <!-- LEFT: Payment Table -->
            <div class="col-lg-8">
                <div class="card mb-3">
                    <div class="card-header bg-light d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Payment History</h5>
                        <h5 class="mb-0 text-success">Total Paid: @totalPaid</h5>
                    </div>

                    <div class="card-body p-0">
                        <table class="table table-striped table-hover mb-0">
                            <thead class="thead-light">
                                <tr>

                                    <th class="text-center">Installment</th>       <!-- emphasized -->
                                    <th class="text-center">Date</th>              <!-- emphasized -->
                                    <th>Program</th>

                                    <th class="text-end">Paid</th>                <!-- emphasized -->

                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in history)
                                {
                                    <tr>

                                        <td class="text-center">
                                            <span class="badge bg-primary">@item.Installment</span>
                                        </td>
                                        <td class="text-center">
                                            <span class="text-muted">
                                                @PersianDateConverter.ToPersianString(item.PaymentDate)
                                            </span>
                                        </td>
                                        <td>@item.ProgramName</td>

                                        <td class="text-end">
                                            <span class="text-success">@item.PaidAmount.ToString("N0")</span>
                                        </td>

                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>


    }
</div>

@code {
    [Parameter] public int StudentId { get; set; }
    [Parameter] public int ProgramId { get; set; }
    [Parameter] public int RefereshToken { get; set; }
    private string totalPaid;

    private List<StudentPaymentModel> history;

    protected override async Task OnParametersSetAsync()
    {
        var allPayments = await historyRepository
            .GetAllAsyncById("GetStudentPaymentHistory", new { StudentId });
       
        history = allPayments
            .Where(p => p.ProgramId == ProgramId)
            .OrderBy(p => p.Installment)
            .ToList();
        if (history.Any())
        {
            totalPaid = history[0].TotalPaid.ToString("N0");
        }
        else
        {
            totalPaid = "0";
        }
    }
}
