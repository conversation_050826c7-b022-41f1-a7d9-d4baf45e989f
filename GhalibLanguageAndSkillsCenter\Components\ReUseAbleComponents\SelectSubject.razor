﻿
<select class="form-control" @onchange="OnSubjectChange">
    <option value="">-- Select Subject --</option>
    @foreach (var subject in subjectList)
    {
        <option value="@subject.SubjectId">@subject.Name</option>
    }
</select>

@code {
    public List<SubjectModel> subjectList { get; set; } = new();

    [Parameter]
    public int SelectedSubjectId { get; set; }

    [Parameter]
    public EventCallback<int> SelectedSubjectIdChanged { get; set; }

    [Parameter]
    public int ProgramId { get; set; } // Used to fetch subjects for a specific program

    [Inject]
    public IGenericRepository<SubjectModel> SubjectRepo { get; set; }

    protected override async Task OnParametersSetAsync()
    {
        // This ensures that the subjects list is updated every time the ProgramId changes
        if (ProgramId != 0)
        {
            await LoadSubjects();
        }
    }

    private async Task LoadSubjects()
    {
        if (ProgramId != 0) // Ensure ProgramId is valid before fetching subjects
        {
            var subjects = await SubjectRepo.GetAllAsyncById("GetSubjectsByProgram",new{ProgramId = ProgramId});
            subjectList = subjects.ToList();
        }
    }

    private async Task OnSubjectChange(ChangeEventArgs e)
    {
        if (int.TryParse(e.Value?.ToString(), out var id))
        {
            SelectedSubjectId = id;
            await SelectedSubjectIdChanged.InvokeAsync(id);
        }
    }
}
