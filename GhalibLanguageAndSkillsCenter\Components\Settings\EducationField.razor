﻿@page "/education-feild"
@inject IGenericRepository<StaffEducationFeild> FeildRepo
@inject IJSRuntime JS
@using System.ComponentModel.DataAnnotations
@rendermode InteractiveServer
@attribute [Authorize]

<h3>@(isEditMode ? "Edit Education Feild" : "Add Education Feild")</h3>

<EditForm Model="@newFeild" OnValidSubmit="AddOrUpdateFeild">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="form-group mb-3">
        <label>English Name</label>
        <InputText class="form-control" @bind-Value="newFeild.EnglishName" />
        <ValidationMessage For="@(() => newFeild.EnglishName)" />
    </div>

    <div class="form-group mb-3">
        <label>Dari Name</label>
        <InputText class="form-control" @bind-Value="newFeild.DariName" />
        <ValidationMessage For="@(() => newFeild.DariName)" />
    </div>

    <div class="form-group mb-3">
        <label>Pashto Name</label>
        <InputText class="form-control" @bind-Value="newFeild.PashtoName" />
        <ValidationMessage For="@(() => newFeild.PashtoName)" />
    </div>

    <button type="submit" class="btn btn-primary">
        @(isEditMode ? "Update" : "Add")
    </button>
    @if (isEditMode)
    {
        <button type="button" class="btn btn-secondary ms-2" @onclick="CancelEdit">Cancel</button>
    }
</EditForm>

<hr />

<h4>Education Feilds</h4>

@if (feilds.Any())
{
    <table class="table table-striped">
        <thead>
            <tr>
                <th>ID</th>
                <th>English</th>
                <th>Dari</th>
                <th>Pashto</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var f in feilds)
            {
                <tr>
                    <td>@f.EducationFeildId</td>
                    <td>@f.EnglishName</td>
                    <td>@f.DariName</td>
                    <td>@f.PashtoName</td>
                    <td>
                        <button class="btn btn-warning btn-sm" @onclick="() => EditFeild(f)">Edit</button>
                        <button class="btn btn-danger btn-sm ms-2" @onclick="() => ConfirmDelete(f.EducationFeildId)">Delete</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
else
{
    <p>No records found.</p>
}

@code {
    private StaffEducationFeild newFeild = new();
    private List<StaffEducationFeild> feilds = new();
    private bool isEditMode = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadFeilds();
    }

    private async Task LoadFeilds()
    {
        feilds = (await FeildRepo.GetAllAsync("GetAllEducationFeilds")).ToList();
    }

    private async Task AddOrUpdateFeild()
    {
        if (isEditMode)
        {
            await FeildRepo.UpdateAsync("UpdateEducationFeild", newFeild);
            await ShowAlert("Feild updated.");
        }
        else
        {
            await FeildRepo.AddAsync("CreateEducationFeild", new
            {
                EnglishName = newFeild.EnglishName,
                DariName = newFeild.DariName,
                PashtoName = newFeild.PashtoName
            });
            await ShowAlert("Feild added.");
        }

        newFeild = new();
        isEditMode = false;
        await LoadFeilds();
    }

    private void EditFeild(StaffEducationFeild f)
    {
        isEditMode = true;
        newFeild = new StaffEducationFeild
            {
                EducationFeildId = f.EducationFeildId,
                EnglishName = f.EnglishName,
                DariName = f.DariName,
                PashtoName = f.PashtoName
            };
    }

    private void CancelEdit()
    {
        isEditMode = false;
        newFeild = new();
    }

    private async Task ConfirmDelete(int id)
    {
        bool confirm = await JS.InvokeAsync<bool>("confirm", "Are you sure?");
        if (confirm)
        {
            await FeildRepo.DeleteAsync("DeleteEducationFeild", new { EducationFeildId = id });
            await LoadFeilds();
            await ShowAlert("Deleted.");
        }
    }

    private async Task ShowAlert(string msg)
    {
        await JS.InvokeVoidAsync("alert", msg);
    }

   
}
