﻿@page "/country"
@using <PERSON><PERSON>
@using Microsoft.Data.SqlClient
@using System.ComponentModel.DataAnnotations
@using System.Data
@inject IConfiguration Configuration
@rendermode InteractiveServer
@attribute [Authorize]

<h3>Country</h3>

<EditForm Model="@newCountry" OnValidSubmit="AddOrUpdateCountry" FormName="country">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="form-group">
        <label for="englishName">English Name</label>
        <InputText id="englishName" class="form-control" @bind-Value="newCountry.EnglishName" />
    </div>

    <div class="form-group">
        <label for="pashtoName">Pashto Name</label>
        <InputText id="pashtoName" class="form-control" @bind-Value="newCountry.CountryPashto" />
    </div>

    <button class="btn btn-primary mt-2" type="submit">
        @if (isEditMode)
        {
            <text>Update Country</text>
        }
        else
        {
            <text>Add Country</text>
        }
    </button>

    @if (isEditMode)
    {
        <button class="btn btn-secondary mt-2 ms-2" type="button" @onclick="CancelEdit">Cancel</button>
    }
</EditForm>

@if (isSuccess)
{
    <p class="alert alert-success mt-2">Operation completed successfully!</p>
}

@if (isError)
{
    <p class="alert alert-danger mt-2">Error: @errorMessage</p>
}

<hr />

<h3>Countries List</h3>

@if (countries is not null && countries.Any())
{
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Id</th>
                <th>English Name</th>
                <th>Pashto Name</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var country in countries)
            {
                <tr>
                    <td>@country.CountryId</td>
                    <td>@country.EnglishName</td>
                    <td>@country.CountryPashto</td>
                    <td>
                        <button class="btn btn-warning btn-sm" @onclick="() => EditCountry(country)">Edit</button>
                        <button class="btn btn-danger btn-sm ms-2" @onclick="() => ConfirmDeleteCountry(country.CountryId)">Delete</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
else
{
    <p>No countries available.</p>
}

<!-- Confirmation Modal -->
@if (isDeleteConfirmationVisible)
{
    <div class="modal" style="display:block;">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="close" aria-label="Close" @onclick="CancelDelete">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this country?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" @onclick="DeleteCountry">Delete</button>
                    <button type="button" class="btn btn-secondary" @onclick="CancelDelete">Cancel</button>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private CountryModel newCountry = new CountryModel();
    private List<CountryModel> countries = new List<CountryModel>();

    private bool isSuccess = false;
    private bool isError = false;
    private string errorMessage = "";

    private bool isEditMode = false;
    private bool isDeleteConfirmationVisible = false;
    private int countryIdToDelete;

    private string connectionString;

    protected override void OnInitialized()
    {
        connectionString = Configuration.GetConnectionString("DefaultConnection");
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadCountries();
    }

    private async Task LoadCountries()
    {
        try
        {
            using var connection = new SqlConnection(connectionString);
            var result = await connection.QueryAsync<CountryModel>(
                "usp_GetAllCountries",
                commandType: CommandType.StoredProcedure);
            countries = result.ToList();
        }
        catch (Exception ex)
        {
            isError = true;
            errorMessage = $"Error loading countries: {ex.Message}";
        }
    }

    private async Task AddOrUpdateCountry()
    {
        try
        {
            using var connection = new SqlConnection(connectionString);
            var parameters = new DynamicParameters();

            if (isEditMode)
            {
                parameters.Add("@CountryId", newCountry.CountryId, DbType.Byte);
                parameters.Add("@Country", newCountry.EnglishName, DbType.String);
                parameters.Add("@CountryPashto", newCountry.CountryPashto, DbType.String);

                await connection.ExecuteAsync(
                    "usp_UpdateCountry",
                    parameters,
                    commandType: CommandType.StoredProcedure);
            }
            else
            {
                parameters.Add("@Country", newCountry.EnglishName, DbType.String);
                parameters.Add("@CountryPashto", newCountry.CountryPashto, DbType.String);
                parameters.Add("@NewCountryId", dbType: DbType.Byte, direction: ParameterDirection.Output);

                await connection.ExecuteAsync(
                    "usp_CreateCountry",
                    parameters,
                    commandType: CommandType.StoredProcedure);

                // Optionally capture the new ID
                newCountry.CountryId = parameters.Get<byte>("@NewCountryId");
            }

            isSuccess = true;
            isError = false;

            newCountry = new CountryModel();
            isEditMode = false;

            await LoadCountries();
        }
        catch (Exception ex)
        {
            isError = true;
            errorMessage = $"An error occurred: {ex.Message}";
        }
    }

    private void EditCountry(CountryModel country)
    {
        isEditMode = true;
        newCountry = new CountryModel
        {
            CountryId = country.CountryId,
            EnglishName = country.EnglishName,
            CountryPashto = country.CountryPashto
        };
    }

    private void CancelEdit()
    {
        isEditMode = false;
        newCountry = new CountryModel();
    }

    private void ConfirmDeleteCountry(int countryId)
    {
        countryIdToDelete = countryId;
        isDeleteConfirmationVisible = true;
    }

    private async Task DeleteCountry()
    {
        try
        {
            using var connection = new SqlConnection(connectionString);
            var parameters = new DynamicParameters();
            parameters.Add("@CountryId", countryIdToDelete, DbType.Byte);

            await connection.ExecuteAsync(
                "usp_DeleteCountry",
                parameters,
                commandType: CommandType.StoredProcedure);

            await LoadCountries();

            isSuccess = true;
            isError = false;
            isDeleteConfirmationVisible = false;
        }
        catch (Exception ex)
        {
            isError = true;
            errorMessage = $"An error occurred: {ex.Message}";
            isDeleteConfirmationVisible = false;
        }
    }

    private void CancelDelete()
    {
        isDeleteConfirmationVisible = false;
    }

    public class CountryModel
    {
        public byte CountryId { get; set; }
        [Required(ErrorMessage = "Please enter a English Name.")]
        public string EnglishName { get; set; }
        [Required(ErrorMessage = "Please enter a Pashto Name.")]
        public string CountryPashto { get; set; }
    }
}
