﻿@implements IDisposable
@inject NavigationManager NavigationManager
@rendermode InteractiveServer
<div class="page">
    <div class="top-row pe-3 navbar navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand fs-8" href="">مرکز زبان و مهارت‌های غالب</a>
        </div>
    </div>

    <input type="checkbox" title="منوی ناوبری" class="navbar-toggler" />

    <div class="nav-scrollable text-end" onclick="document.querySelector('.navbar-toggler').click()">
        <nav class="nav flex-column overflow-x-hidden">

            <!-- Home -->
            <div class="nav-item me-3 w-100">
                <NavLink class="nav-link" href="" Match="NavLinkMatch.All">
                    <span class="bi bi-graph-up me-2 nav-icon"></span> داشبورد
                </NavLink>
            </div>
            <div class="nav-item me-3 w-100">
                <button type="button" class="nav-link d-flex justify-content-between align-items-center" @onclick="ToggleEnrollment">
                    <span><span class="bi bi-journal-plus me-2 nav-icon"></span> پذیزش</span>
                    <span class="bi" style="transform: rotate(@(showEnrollment ? 90 : 0)deg); transition: transform .2s;">&#9656;</span>
                </button>
                @if (showEnrollment)
                {
                    <ul class="nav flex-column ms-4">

                        <li class="nav-item">
                            <NavLink class="nav-link" href="enroll" Match="NavLinkMatch.Prefix">
                                <span class="bi bi-credit-card me-2 nav-icon"></span> ثبت نام
                            </NavLink>
                        </li>
                        <li class="nav-item">
                            <NavLink class="nav-link" href="student" Match="NavLinkMatch.Prefix">

                                <span class="bi bi-person-lines-fill me-2 nav-icon"></span> شاگردان

                            </NavLink>
                        </li>
                        
                        <li>
                            <NavLink class="nav-link" href="/home" Match="NavLinkMatch.All">
                                <span class="bi bi-house-door me-2 nav-icon"></span> تمام ثبت نام ها
                            </NavLink>
                        </li>
                    </ul>
                }
            </div>

            <!-- Finance Group -->
            <div class="nav-item me-3 w-100">
                <button type="button" class="nav-link d-flex justify-content-between align-items-center" @onclick="ToggleFinance">
                    <span><span class="bi bi-cash-stack me-2 nav-icon"></span> مالی</span>
                    <span class="bi" style="transform: rotate(@(showFinance ? 90 : 0)deg); transition: transform .2s;">&#9656;</span>
                </button>
                @if (showFinance)
                {
                    <ul class="nav flex-column ms-4">
                        <li class="nav-item">
                            <NavLink class="nav-link" href="payment" Match="NavLinkMatch.Prefix">
                                <span class="bi bi-credit-card me-2 nav-icon"></span> فیس
                            </NavLink>
                        </li>

                        <li class="nav-item">
                            <NavLink class="nav-link" href="reports" Match="NavLinkMatch.Prefix">
                                <span class="bi bi bi-journal-bookmark me-2 nav-icon"></span> گزارشات پرداختی 
                            </NavLink>
                        </li>
                        <li class="nav-item">
                            <NavLink class="nav-link" href="staffsalary" Match="NavLinkMatch.Prefix">
                                <span class="bi bi bi-credit-card me-2 nav-icon"></span> معاش کارمندان
                            </NavLink>
                        </li>
                    </ul>
                }
            </div>

            <!-- Timetable -->
            <div class="nav-item me-3 w-100">
                <NavLink class="nav-link" href="timetable">
                    <span class="bi bi-clock me-2 nav-icon"></span> تقسیم اوقات
                </NavLink>
            </div>

            <!-- Staff -->
            <div class="nav-item me-3 w-100">
                <NavLink class="nav-link" href="staff">
                    <span class="bi bi-person-badge me-2 nav-icon"></span> مدیریت کارمندان
                </NavLink>
            </div>

           

            <!-- Settings -->
            <div class="nav-item me-3 w-100">
                <NavLink class="nav-link" href="settings">
                    <span class="bi bi-gear-fill me-2 nav-icon"></span> تنظیمات
                </NavLink>
            </div>

        </nav>
    </div>
</div>

@code {
    private bool showEnrollment;
    private bool showFinance;
    private string? currentUrl;

    protected override void OnInitialized()
    {
        currentUrl = NavigationManager.ToBaseRelativePath(NavigationManager.Uri);
        NavigationManager.LocationChanged += OnLocationChanged;
        UpdateMenuState(currentUrl);
    }

    private void ToggleEnrollment()
    {
        showEnrollment = !showEnrollment;
        showFinance = false;
    }

    private void ToggleFinance()
    {
        showFinance = !showFinance;
        showEnrollment = false;
    }

    private void OnLocationChanged(object? sender, Microsoft.AspNetCore.Components.Routing.LocationChangedEventArgs e)
    {
        currentUrl = NavigationManager.ToBaseRelativePath(e.Location);
        UpdateMenuState(currentUrl);
        StateHasChanged();
    }

    private void UpdateMenuState(string? url)
    {
        // Collapse all by default
        showEnrollment = false;
        showFinance = false;

        if (!string.IsNullOrEmpty(url))
        {
            if (url.StartsWith("student") || url.StartsWith("enroll"))
            {
                showEnrollment = true;
            }
            else if (url.StartsWith("payment") || url.StartsWith("reports"))
            {
                showFinance = true;
            }
        }
    }

    public void Dispose()
    {
        NavigationManager.LocationChanged -= OnLocationChanged;
    }
}
