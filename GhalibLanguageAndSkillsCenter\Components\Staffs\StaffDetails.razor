﻿@page "/staff-details/{StaffId:int}"
@inject IGenericRepository<StaffDetailDto> staffDetailsRepo
@inject IGenericRepository<StaffEducationModel> staffEducationRepo
@inject IGenericRepository<StaffEducationFeild> fieldRepo
@inject IGenericRepository<StaffDegreeModel> degreeRepo
@inject IGenericRepository<UniversityModel> universityRepo
@inject IGenericRepository<PositionModel> positionRepo
@inject IGenericRepository<CountryDto> countryRepo
@inject IGenericRepository<ProvinceModel> provinceRepo
@rendermode InteractiveServer
@attribute [Authorize]

@if (!string.IsNullOrEmpty(successmessage))
{
    <div class="alert alert-success alert-dismissible fade show fixed-top m-3 shadow" role="alert" style="z-index: 1050;">
        @successmessage
        <button type="button" class="btn-close" @onclick="() => successmessage = null" aria-label="Close"></button>
    </div>
}

@if (staff == null)
{
    <p>Loading...</p>
}
else
{
    <EditForm Model="this" OnValidSubmit="SaveAllAsync">
        <DataAnnotationsValidator />
        <div class="container mt-5 staff-details-container">
            <div class="card shadow-lg border-0 rounded-4 p-4 bg-white">
                <!-- Staff Edit Section -->
                <div class="text-center mb-4">
                    <label for="fileInput" class="pointer">
                        @if (staff.ProfileImage != null)
                        {
                            <img src="data:image/jpeg;base64,@Convert.ToBase64String(staff.ProfileImage)" class="profile-img-large rounded-circle shadow" />
                        }
                        else
                        {
                            <div class="no-image">Click to add image</div>
                        }
                    </label>
                    <InputFile id="fileInput" style="display:none" OnChange="OnProfileImageChange" />
                </div>
                <div class="row g-3 mb-4">
                    <div class="col-md-6">
                        <label>Name</label>
                        <InputText class="form-control" @bind-Value="staff.StaffName" />
                    </div>
                    <div class="col-md-6">
                        <label>Last Name</label>
                        <InputText class="form-control" @bind-Value="staff.LastName" />
                    </div>
                    <div class="col-md-6">
                        <label>Father Name</label>
                        <InputText class="form-control" @bind-Value="staff.FatherName" />
                    </div>
                    <div class="col-md-3">
                        <label>Age</label>
                        <InputNumber class="form-control" @bind-Value="staff.Age" />
                    </div>
                    <div class="col-md-3">
                        <label>Position</label>
                        <InputSelect class="form-select" @bind-Value="staff.PositionId">
                            <option value="0">-- Select Position --</option>
                            @foreach (var pos in positions)
                            {
                                <option value="@pos.PositionId">@pos.EnglishName</option>
                            }
                        </InputSelect>
                    </div>
                </div>

                <hr />

                <!-- Education Sections -->
                @if(staffDetails[0].StaffEducationId >0)
                {
                    
                
                    <h4 class="mb-3">🎓 Education History</h4>
                  @foreach (var edu in staffDetails)
                  {
                    <div class="border rounded-3 p-3 mb-3 bg-light edu-card">
                        <InputNumber @bind-Value="edu.StaffEducationId" hidden />
                        <div class="row g-2">
                            <div class="col-md-4">
                                <label>📘 Field</label>
                                <InputSelect class="form-select" @bind-Value="edu.EducationFeildId">
                                    <option value="0">-- Select Field --</option>
                                    @foreach (var field in fieldList)
                                    {
                                        <option value="@field.EducationFeildId">@field.EnglishName</option>
                                    }
                                </InputSelect>
                            </div>
                            <div class="col-md-4">
                                <label>🎓 Degree</label>
                                <InputSelect class="form-select" @bind-Value="edu.EducationDegreeId">
                                    <option value="0">-- Select Degree --</option>
                                    @foreach (var degree in degreeList)
                                    {
                                        <option value="@degree.EducationDegreeId">@degree.EnglishName</option>
                                    }
                                </InputSelect>
                            </div>
                            <div class="col-md-4">
                                <label>🏫 University</label>
                                <InputSelect class="form-select" @bind-Value="edu.UniversityId">
                                    <option value="0">-- Select University --</option>
                                    @foreach (var uni in universityList)
                                    {
                                        <option value="@uni.UniversityId">@uni.EnglishName</option>
                                    }
                                </InputSelect>
                            </div>
                            <div class="col-md-6">
                                <label>🌍 Country</label>
                                <select class="form-select" value="@GetCountryForEducation(edu.StaffEducationId)"
                                        @onchange="@(e => UpdateCountryForEducation(edu.StaffEducationId, Convert.ToInt32(e.Value)))">
                                    <option value="0">-- Select Country --</option>
                                    @foreach (var country in countryList)
                                    {
                                        <option value="@country.CountryId">@country.Country</option>
                                    }
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label>🏙️ Province</label>
                                <InputSelect class="form-select" @bind-Value="edu.ProvinceId">
                                    <option value="0">-- Select Province --</option>
                                    @foreach (var province in GetProvincesForEducation(edu.StaffEducationId))
                                    {
                                        <option value="@province.ProvinceId">@province.Province</option>
                                    }
                                </InputSelect>
                            </div>
                            <div class="col-md-6">
                                <label>Start Date</label>
                                <InputDate class="form-control" @bind-Value="edu.EducationStartingDate" />
                            </div>
                            <div class="col-md-6">
                                <label>End Date</label>
                                <InputDate class="form-control" @bind-Value="edu.EducationEndingDate" />
                            </div>
                            <div class="col-md-6">
                                <label>Grading Scale</label>
                                <InputNumber class="form-control" @bind-Value="edu.GradingScale" />
                            </div>
                        </div>
                    </div>
                }
                }
                <!-- Add New Education -->
                <h4 class="mt-4 mb-3">➕ Add New Education</h4>
                <div class="border rounded-3 p-3 mb-3 bg-white edu-card">
                    <div class="row g-2">
                        <div class="col-md-4">
                            <label>📘 Field</label>
                            <InputSelect class="form-select" @bind-Value="newEducation.EducationFeild">
                                <option value="0">-- Select Field --</option>
                                @foreach (var field in fieldList)
                                {
                                    <option value="@field.EducationFeildId">@field.EnglishName</option>
                                }
                            </InputSelect>
                        </div>
                        <div class="col-md-4">
                            <label>🎓 Degree</label>
                            <InputSelect class="form-select" @bind-Value="newEducation.EducationDegreeId">
                                <option value="0">-- Select Degree --</option>
                                @foreach (var degree in degreeList)
                                {
                                    <option value="@degree.EducationDegreeId">@degree.EnglishName</option>
                                }
                            </InputSelect>
                        </div>
                        <div class="col-md-4">
                            <label>🏫 University</label>
                            <InputSelect class="form-select" @bind-Value="newEducation.UniversityId">
                                <option value="0">-- Select University --</option>
                                @foreach (var uni in universityList)
                                {
                                    <option value="@uni.UniversityId">@uni.EnglishName</option>
                                }
                            </InputSelect>
                        </div>
                        <div class="col-md-6">
                            <label>🌍 Country</label>
                            <select class="form-select" value="@newEducationCountryId"
                                    @onchange="@(e => UpdateNewEducationCountry(Convert.ToInt32(e.Value)))">
                                <option value="0">-- Select Country --</option>
                                @foreach (var country in countryList)
                                {
                                    <option value="@country.CountryId">@country.Country</option>
                                }
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label>🏙️ Province</label>
                            <InputSelect class="form-select" @bind-Value="newEducation.ProvinceId">
                                <option value="0">-- Select Province --</option>
                                @foreach (var province in newEducationProvinces)
                                {
                                    <option value="@province.ProvinceId">@province.Province</option>
                                }
                            </InputSelect>
                        </div>
                        <div class="col-md-6">
                            <label>Start Date</label>
                            <InputDate class="form-control" @bind-Value="newEducation.EducationStartingDate" />
                        </div>
                        <div class="col-md-6">
                            <label>End Date</label>
                            <InputDate class="form-control" @bind-Value="newEducation.EducationEndingDate" />
                        </div>
                        <div class="col-md-6">
                            <label>Grading Scale</label>
                            <InputNumber class="form-control" @bind-Value="newEducation.GradingScale" />
                        </div>
                    </div>
                </div>

                <!-- Single Save Button -->
                <div class="text-end mt-3">
                    <button type="submit" class="btn btn-success btn-lg">💾 Save All Changes</button>
                </div>
            </div>
        </div>
    </EditForm>
}

@code {
    [Parameter]
    public int StaffId { get; set; }

    private StaffDetailDto staff;
    private List<StaffDetailDto> staffDetails = new();
    private StaffEducationModel newEducation = new() { EducationStartingDate = DateTime.Today, EducationEndingDate = DateTime.Today };
    private List<StaffEducationFeild> fieldList = new();
    private List<StaffDegreeModel> degreeList = new();
    private List<UniversityModel> universityList = new();
    private List<PositionModel> positions = new();
    private List<CountryDto> countryList = new();
    private List<ProvinceModel> provinceList = new();
    private string? successmessage;

    // Education-specific country and province tracking
    private Dictionary<int, int> educationCountryMap = new();
    private Dictionary<int, List<ProvinceModel>> educationProvinceMap = new();
    private int newEducationCountryId = 0;
    private List<ProvinceModel> newEducationProvinces = new();

    protected override async Task OnInitializedAsync()
    {
        fieldList = (await fieldRepo.GetAllAsync("GetAllEducationFeilds")).ToList();
        degreeList = (await degreeRepo.GetAllAsync("GetAllEducationDegrees")).ToList();
        universityList = (await universityRepo.GetAllAsync("GetAllUniversities")).ToList();
        positions = (await positionRepo.GetAllAsync("GetAllPositions")).ToList();
        countryList = (await countryRepo.GetAllAsync("usp_GetAllCountries")).ToList();
        provinceList = (await provinceRepo.GetAllAsync("usp_GetAllProvinces")).ToList();
        await LoadDetails();
    }

    private int GetCountryForEducation(int educationId)
    {
        if (educationCountryMap.ContainsKey(educationId))
            return educationCountryMap[educationId];

        // Try to find the country based on the province
        var edu = staffDetails.FirstOrDefault(e => e.StaffEducationId == educationId);
        if (edu != null && edu.ProvinceId > 0)
        {
            var province = provinceList.FirstOrDefault(p => p.ProvinceId == edu.ProvinceId);
            if (province != null)
            {
                educationCountryMap[educationId] = province.CountryId;
                return province.CountryId;
            }
        }

        return 0;
    }

    private List<ProvinceModel> GetProvincesForEducation(int educationId)
    {
        if (educationProvinceMap.ContainsKey(educationId))
            return educationProvinceMap[educationId];

        int countryId = GetCountryForEducation(educationId);
        var provinces = provinceList.Where(p => p.CountryId == countryId).ToList();
        educationProvinceMap[educationId] = provinces;
        return provinces;
    }

    private void UpdateCountryForEducation(int educationId, int countryId)
    {
        educationCountryMap[educationId] = countryId;
        educationProvinceMap[educationId] = provinceList.Where(p => p.CountryId == countryId).ToList();

        // Reset province selection when country changes
        var edu = staffDetails.FirstOrDefault(e => e.StaffEducationId == educationId);
        if (edu != null)
        {
            edu.ProvinceId = 0;
        }

        StateHasChanged();
    }

    private void UpdateNewEducationCountry(int countryId)
    {
        newEducationCountryId = countryId;
        newEducationProvinces = provinceList.Where(p => p.CountryId == countryId).ToList();
        newEducation.ProvinceId = 0;
        StateHasChanged();
    }

    private async Task LoadDetails()
    {
        try
        {
            staffDetails = (await staffDetailsRepo.GetAllAsyncById("StaffDetails", new { StaffId })).ToList();
            staff = staffDetails.FirstOrDefault();

            if (staff == null)
            {
                staff = new StaffDetailDto
                    {
                        StaffId = StaffId,
                        StaffName = "",
                        LastName = "",
                        FatherName = "",
                        Age = 0,
                        PositionId = 0
                    };
            }

            // Initialize country and province maps for existing education records
            educationCountryMap.Clear();
            educationProvinceMap.Clear();

            foreach (var edu in staffDetails)
            {
                if (edu.ProvinceId > 0)
                {
                    var province = provinceList.FirstOrDefault(p => p.ProvinceId == edu.ProvinceId);
                    if (province != null)
                    {
                        educationCountryMap[edu.StaffEducationId] = province.CountryId;
                        educationProvinceMap[edu.StaffEducationId] = provinceList
                            .Where(p => p.CountryId == province.CountryId)
                            .ToList();
                    }
                }
            }

            newEducation = new StaffEducationModel
                {
                    StaffId = StaffId,
                    EducationStartingDate = DateTime.Today,
                    EducationEndingDate = DateTime.Today
                };

            newEducationCountryId = 0;
            newEducationProvinces = new List<ProvinceModel>();
        }
        catch (Exception ex)
        {
            successmessage = $"Error loading details: {ex.Message}";
        }
    }

    private async Task OnProfileImageChange(InputFileChangeEventArgs e)
    {
        try
        {
            var file = e.File;
            using var ms = new MemoryStream();
            await file.OpenReadStream(5 * 1024 * 1024).CopyToAsync(ms);
            staff.ProfileImage = ms.ToArray();
        }
        catch (Exception ex)
        {
            successmessage = $"Error uploading image: {ex.Message}";
        }
    }

    private async Task SaveAllAsync()
    {
        try
        {
            // Update staff
            await staffDetailsRepo.UpdateAsync("UpdateStaff", new
            {
                StaffId = staff.StaffId,
                Name = staff.StaffName,
                LastName = staff.LastName,
                FatherName = staff.FatherName,
                Age = staff.Age,
                PositionId = staff.PositionId,
                ProfileImage = staff.ProfileImage
            });

            // Update existing educations
            foreach (var edu in staffDetails)
            {
                await staffEducationRepo.UpdateAsync("UpdateStaffEducation", new
                {
                    edu.StaffEducationId,
                    EducationFeild = edu.EducationFeildId,
                    UniversityId = edu.UniversityId,
                    EducationDegreeId = edu.EducationDegreeId,
                    StaffId,
                    edu.EducationStartingDate,
                    edu.EducationEndingDate,
                    edu.GradingScale,
                    edu.ProvinceId
                });
            }

            // Add new education if fields are selected
            if (newEducation.EducationFeild != 0 && newEducation.EducationDegreeId != 0 && newEducation.UniversityId != 0)
            {
                await staffEducationRepo.AddAsync("CreateStaffEducation", new
                {
                    EducationFeild = newEducation.EducationFeild,
                    EducationDegreeId = newEducation.EducationDegreeId,
                    UniversityId = newEducation.UniversityId,
                    StaffId,
                    newEducation.EducationStartingDate,
                    newEducation.EducationEndingDate,
                    newEducation.GradingScale,
                    newEducation.ProvinceId
                });
            }

            successmessage = "All changes saved successfully!";
            await LoadDetails();
        }
        catch (Exception ex)
        {
            successmessage = $"Error saving changes: {ex.Message}";
        }
    }

    public class StaffDetailDto
    {
        public int StaffEducationId { get; set; }
        public int StaffId { get; set; }
        public string StaffName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string FatherName { get; set; } = string.Empty;
        public int Age { get; set; }
        public int PositionId { get; set; }
        public byte[]? ProfileImage { get; set; }

        public int EducationFeildId { get; set; }
        public string EducationFeild { get; set; } = string.Empty;
        public int EducationDegreeId { get; set; }
        public string Degree { get; set; } = string.Empty;
        public int UniversityId { get; set; }
        public string University { get; set; } = string.Empty;
        public DateTime? EducationStartingDate { get; set; }
        public DateTime? EducationEndingDate { get; set; }
        public int? GradingScale { get; set; }
        public int ProvinceId { get; set; }
        public string Province { get; set; } = string.Empty;
    }

    public class CountryDto
    {
        public int CountryId { get; set; }
        public string Country { get; set; } = string.Empty;
    }

    public class ProvinceModel
    {
        public int ProvinceId { get; set; }
        public string Province { get; set; } = string.Empty;
        public int CountryId { get; set; }
    }
}

<style>
    .staff-details-container {
        max-width: 900px;
        margin: auto;
    }

    .profile-img-large {
        width: 150px;
        height: 150px;
        object-fit: cover;
        border: 5px solid #dee2e6;
        background-color: #f8f9fa;
        cursor: pointer;
    }

    .no-image {
        width: 150px;
        height: 150px;
        line-height: 150px;
        text-align: center;
        border-radius: 50%;
        background-color: #e0e0e0;
        font-weight: bold;
        color: #6c757d;
        border: 5px dashed #ccc;
        margin: auto;
        cursor: pointer;
    }

    .edu-card {
        background-color: #f8f9fa;
        transition: 0.3s ease;
    }

        .edu-card:hover {
            background-color: #e9ecef;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
</style>