﻿@using GhalibLanguageAndSkillsCenter.Models.Payment
@inject IJSRuntime jsRuntime
@rendermode InteractiveServer
@using Moraba

@if (PrintStudentBill.StudentId != 0)
{
    <div class="modal show d-block modal-backdrop-custom" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content border-0 bg-transparent shadow-none p-0">

                <div id="printable-receipt" class="receipt-box p-4">
                    <!-- Header with Logo and Title -->
                    <div class="receipt-header mb-3 d-flex flex-row-reverse align-items-center justify-content-between">
                        <img src="/images/logo.png" alt="Logo" style="height: 60px;" />
                        <div class="text-center flex-fill fw-bold pe-4" style="font-size: 20px;">
                            <div>مرکز زبان و مهارت های غالب</div>
                            <div>آمریت مالی</div>
                            <div>رسید پرداخت پول</div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between flex-wrap">
                        <div class="info-pair">
                            <span class="label">نام:</span>
                            <span class="value">@PrintStudentBill.StudentName</span>
                        </div>
                        <div class="info-pair">
                            <span class="label">ولد:</span>
                            <span class="value">@PrintStudentBill.FatherName</span>
                        </div>
                        <div class="info-pair">
                            <span class="label"> آی دی:</span>
                            <span class="value">@PrintStudentBill.StudentIdNumber</span>
                        </div>
                        <div class="info-pair">
                            <span class="label">پروگرام:</span>
                            <span class="value">@PrintStudentBill.ProgramName</span>
                        </div>
                        <div class="info-pair">
                            <span class="label">دوره:</span>
                            <span class="value"> @Moraba.Persian.Numbers.Convert.NumberToText(PrintStudentBill.Period.ToString())</span>
                        </div>
                        <div class="info-pair">
                            <span class="label"> رسید نمبر:</span>
                            <span class="value">@PrintStudentBill.BillNo</span>
                        </div>
                        <div class="info-pair">
                            <span class="label"> تاریخ پرداختی:</span>
                            <span class="value">@PersianDateConverter.ToPersianString(PrintStudentBill.PaymentDate)</span>
                        </div>
                    </div>

                    <!-- One-row Table -->
                    <table class="receipt-table w-100">
                        <tr>
                            <th class="label-cell"> فیس سمستر:</th>
                            <th class="label-cell">مبلغ پرداختی:</th>
                            <th class="label-cell"> بیلانس:</th>
                        </tr>
                        <tr>
                            <td class="value-cell">@PrintStudentBill.ProgramFee</td>
                            <td class="value-cell">@PrintStudentBill.PaidAmount.ToString("N0")</td>
                            <td class="value-cell">@PrintStudentBill.RemainingAmount.ToString("N0")</td>
                        </tr>
                        <tr>
                            <td colspan="3">مبلغ پرداختی به حروف: @Moraba.Persian.Numbers.Convert.NumberToText(PrintStudentBill.PaidAmount.ToString()) افغانی</td>
                        </tr>
                    </table>
                    
                    <div class="d-flex justify-content-between mt-3 p-4">
                        <p class="signature-label">امضا پرداخت‌کننده:</p>
                        <p class="signature-label">امضا دریافت‌کننده:</p>
                    </div>

                    <!-- Footer Buttons -->
                    <div class="receipt-footer mt-4 no-print text-center">
                        <button class="btn btn-secondary mx-2" @onclick="CloseModal">بند</button>
                        <button class="btn btn-primary mx-2" @onclick="PrintNow">
                            <i class="bi bi-printer me-1"></i> چاپ
                        </button>
                    </div>
                </div>

            </div>
        </div>
    </div>
}

@code {
    [Parameter]
    public StudentPaymentModel PrintStudentBill { get; set; } = new();

    [Parameter]
    public EventCallback OnClose { get; set; }
   
    private async Task PrintNow()
    {
        await jsRuntime.InvokeVoidAsync("printReceipt");
    }

    private async Task CloseModal()
    {
        PrintStudentBill = new();
        await OnClose.InvokeAsync(null);
    }
}

<script>

        window.printReceipt = () => {
        // Get the receipt content
        const receiptContent = document.getElementById('printable-receipt');

        if (!receiptContent) {
            console.error('Receipt content not found');
            return;
        }

        // Create a new window for printing
        const printWindow = window.open('', '_blank', 'width=800,height=600');

        // Write the content to the new window with EXACT same styling
        printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Receipt</title>
                    <meta charset="utf-8">
                    <style>
                        body {
                            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                            direction: rtl;
                            margin: 20px;
                            color: #1a1a4a;
                            background: white;
                        }

                        .receipt-box {
                            background-color: #fdeef2;
                            border: 1px solid #aaa;
                            border-radius: 8px;
                            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
                            width: 800px;
                            direction: rtl;
                            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                            color: #1a1a4a;
                            margin: auto;
                            padding: 1.5rem;
                        }

                        .receipt-header {
                            border-bottom: 1px dashed #1a1a4a;
                            padding-bottom: 8px;
                            margin-bottom: 1rem;
                            display: flex;
                            flex-direction: row-reverse;
                            align-items: center;
                            justify-content: space-between;
                        }

                        .text-center {
                            text-align: center;
                            flex: 1;
                            font-weight: bold;
                            padding-right: 1rem;
                            font-size: 20px;
                        }

                        .flex-fill {
                            flex: 1;
                        }

                        .fw-bold {
                            font-weight: bold;
                        }

                        .pe-4 {
                            padding-right: 1rem;
                        }

                        .d-flex {
                            display: flex;
                        }

                        .justify-content-between {
                            justify-content: space-between;
                        }

                        .flex-wrap {
                            flex-wrap: wrap;
                        }

                        .align-items-center {
                            align-items: center;
                        }

                        .flex-row-reverse {
                            flex-direction: row-reverse;
                        }

                        .mb-3 {
                            margin-bottom: 1rem;
                        }

                        .mt-3 {
                            margin-top: 1rem;
                        }

                        .p-4 {
                            padding: 1.5rem;
                        }

                        .w-100 {
                            width: 100%;
                        }

                        .info-pair {
                            display: flex;
                            align-items: center;
                            margin-bottom: 8px;
                            font-size: 16px;
                            font-weight: 600;
                            gap: 4px;
                            min-width: 200px;
                        }

                        .label {
                            color: #1a1a4a;
                        }

                        .value {
                            color: #1a1a4a;
                        }

                        .receipt-table {
                            border-collapse: collapse;
                            width: 100%;
                        }

                        .receipt-table td,
                        .receipt-table th {
                            padding: 8px 10px;
                            vertical-align: middle;
                            font-size: 17px;
                            border: 1px dotted blue;
                        }

                        .label-cell {
                            font-weight: 600;
                            text-align: right;
                            white-space: nowrap;
                        }

                        .value-cell {
                            text-align: left;
                            white-space: nowrap;
                        }

                        .signature-label {
                            font-weight: 500;
                            text-align: right;
                            white-space: nowrap;
                            margin: 0;
                        }

                        .no-print {
                            display: none;
                        }

                        img {
                            height: 60px;
                            width: auto;
                        }


                    </style>
                </head>
                <body>
                    ${receiptContent.outerHTML}
                </body>
                </html>
            `);

        // Close the document and print
        printWindow.document.close();

        // Wait for content to load then print
        printWindow.onload = function () {
            printWindow.print();
            printWindow.close();
        };
    };
</script>