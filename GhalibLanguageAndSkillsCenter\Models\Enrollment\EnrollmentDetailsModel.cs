﻿namespace GhalibLanguageAndSkillsCenter.Models.Enrollment
{
    public class EnrollmentDetailsModel
    {
        public int EnrollmentId { get; set; }
        public string StudentName { get; set; }
        public string StudentLastName { get; set; }
        public string StudentFatherName { get; set; }
        public int ProgramId { get; set; }
        public string ProgramName { get; set; }
        public DateTime ProgramStartingDate { get; set; }
        public DateTime ProgramEndingDate { get; set; }

        public string CalculatedDuration { get; set; }
        public int SectionId { get; set; }
        public string SectionName { get; set; }
        public int ShiftId { get; set; }
        public string ShiftName { get; set; }
        public DateTime EnrollmentDate { get; set; }
    }
}
