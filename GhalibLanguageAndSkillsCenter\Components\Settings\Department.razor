﻿@page "/department"
@inject IGenericRepository<DepartmentModel> DepartmentRepo
@inject IJSRuntime JS
@rendermode InteractiveServer
@attribute [Authorize]
<h3>Add Department</h3>

<EditForm Model="@newDepartment" OnValidSubmit="AddOrUpdateDepartment" FormName="department">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="form-group">
        <label for="englishName">English Name</label>
        <InputText id="englishName" class="form-control" @bind-Value="newDepartment.EnglishName" />
    </div>

    <div class="form-group">
        <label for="dariName">Dari Name</label>
        <InputText id="dariName" class="form-control" @bind-Value="newDepartment.DariName" />
    </div>

    <div class="form-group">
        <label for="pashtoName">Pashto Name</label>
        <InputText id="pashtoName" class="form-control" @bind-Value="newDepartment.PashtoName" />
    </div>

    <button class="btn btn-primary mt-2" type="submit">
        @if (isEditMode)
        {
            <text>Update Department</text>
        }
        else
        {
            <text>Add Department</text>
        }
    </button>

    @if (isEditMode)
    {
        <button class="btn btn-secondary mt-2 ms-2" type="button" @onclick="CancelEdit">Cancel</button>
    }
</EditForm>

<hr />

<h3>Departments List</h3>

@if (departments is not null && departments.Any())
{
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Id</th>
                <th>English Name</th>
                <th>Dari Name</th>
                <th>Pashto Name</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var dept in departments)
            {
                <tr>
                    <td>@dept.DepartmentId</td>
                    <td>@dept.EnglishName</td>
                    <td>@dept.DariName</td>
                    <td>@dept.PashtoName</td>
                    <td>
                        <button class="btn btn-warning btn-sm" @onclick="() => EditDepartment(dept)">Edit</button>
                        <button class="btn btn-danger btn-sm ms-2" @onclick="() => ConfirmDelete(dept.DepartmentId)">Delete</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
else
{
    <p>No departments available.</p>
}

@code {
    private DepartmentModel newDepartment = new DepartmentModel();
    private List<DepartmentModel> departments = new List<DepartmentModel>();

    private bool isEditMode = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadDepartments();
    }

    private async Task LoadDepartments()
    {
        departments = (await DepartmentRepo.GetAllAsync("AllDepartments")).ToList();
    }

    private async Task AddOrUpdateDepartment()
    {
        try
        {
            if (isEditMode)
            {
                await DepartmentRepo.UpdateAsync("UpdateDepartment",newDepartment);
                await ShowAlert("Department updated successfully!");
            }
            else
            {
                await DepartmentRepo.AddAsync("AddDepartment",new { EnglishName = newDepartment.EnglishName, DariName = newDepartment.DariName, PashtoName = newDepartment.PashtoName });
                await ShowAlert("Department added successfully!");
            }

            newDepartment = new DepartmentModel();
            isEditMode = false;

            await LoadDepartments();
        }
        catch (Exception ex)
        {
            await ShowAlert($"An error occurred: {ex.Message}");
        }
    }

    private void EditDepartment(DepartmentModel department)
    {
        isEditMode = true;
        newDepartment = new DepartmentModel
            {
                DepartmentId = department.DepartmentId,
                EnglishName = department.EnglishName,
                DariName = department.DariName,
                PashtoName = department.PashtoName
            };
    }

    private void CancelEdit()
    {
        isEditMode = false;
        newDepartment = new DepartmentModel();
    }

    private async Task ConfirmDelete(int departmentId)
    {
        bool confirm = await JS.InvokeAsync<bool>("confirm", "Are you sure you want to delete this department?");
        if (confirm)
        {
            await DeleteDepartment(departmentId);
        }
    }

    private async Task DeleteDepartment(int departmentId)
    {
        try
        {
            await DepartmentRepo.DeleteAsync("DeleteDepartment", new { DepartmentId = departmentId });
            await LoadDepartments();
            await ShowAlert("Department deleted successfully!");
        }
        catch (Exception ex)
        {
            await ShowAlert($"An error occurred: {ex.Message}");
        }
    }

    private async Task ShowAlert(string message)
    {
        await JS.InvokeVoidAsync("alert", message);
    }

 
}
