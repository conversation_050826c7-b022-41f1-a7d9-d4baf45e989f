﻿@page "/manage-users"
@using Microsoft.AspNetCore.Identity
@using Microsoft.JSInterop
@inject IJSRuntime JSRuntime

@using Microsoft.AspNetCore.Components.Authorization
@rendermode InteractiveServer
@inject UserManager<ApplicationUser> UserManager
@inject RoleManager<IdentityRole> RoleManager
@inject NavigationManager NavigationManager
@inject AuthenticationStateProvider AuthenticationStateProvider


<h3 class="mb-4 text-center text-primary">Manage Users</h3>

@if (usersWithRoles == null)
{
    <div class="text-center">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
}
else
{
    <div class="card mb-4 shadow-sm">
        <div class="card-body p-0">
            <table class="table table-hover table-bordered mb-0">
                <thead class="table-dark">
                    <tr>
                        <th scope="col">ایمیل</th>
                        <th scope="col">نام یوزر</th>
                        <th scope="col">رول</th>
                        <th scope="col">اعمال</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in usersWithRoles)
                    {
                        <tr>
                            <td>@item.User.Email</td>
                            <td>@item.User.UserName</td>
                            <td>@item.Role</td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary me-2" @onclick="() => EditUser(item)">ویرایش</button>
                                <button class="btn btn-sm btn-outline-danger"
                                        @onclick="() => ConfirmDelete(item.User)">
                                    حذف
                                </button>

                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
}

@if (selectedUser != null)
{
    <div class="card shadow border-0">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Edit User</h5>
        </div>
        <div class="card-body">
            <EditForm Model="editModel" OnValidSubmit="HandleValidSubmit">
                <DataAnnotationsValidator />
                <ValidationSummary />

                <div class="mb-3">
                    <label class="form-label">Email</label>
                    <InputText class="form-control" @bind-Value="editModel.Email" disabled />
                </div>

                <div class="mb-3">
                    <label class="form-label">Username</label>
                    <InputText class="form-control" @bind-Value="editModel.UserName" />
                </div>

                <div class="mb-3">
                    <label class="form-label">Role</label>
                    <InputSelect class="form-select" @bind-Value="editModel.Role">
                        @foreach (var role in roles)
                        {
                            <option value="@role">@role</option>
                        }
                    </InputSelect>
                </div>

                <div class="mb-3">
                    <label class="form-label">New Password</label>
                    <InputText class="form-control" @bind-Value="editModel.NewPassword" type="password" />
                    <div class="form-text">Leave blank to keep the current password.</div>
                </div>

                <div class="d-flex justify-content-end">
                    <button type="submit" class="btn btn-success me-2">Update</button>
                    <button type="button" class="btn btn-secondary" @onclick="CancelEdit">Cancel</button>
                </div>
            </EditForm>
        </div>
    </div>
}

@code {
   

   
    private List<UserWithRoles> usersWithRoles;
    private List<string> roles = new();
    private ApplicationUser selectedUser;
    private EditUserModel editModel = new();

    protected override async Task OnInitializedAsync()
    {
        var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
        var currentUser = authState.User;

        if (currentUser == null || !currentUser.Identity.IsAuthenticated)
        {
            NavigationManager.NavigateTo("/");
            return;
        }

        var user = await UserManager.GetUserAsync(currentUser);
        var userRoles = await UserManager.GetRolesAsync(user);

        if (userRoles.Contains("Administrator"))
        {
            await LoadUsersAndRoles();
        }
        else
        {
            NavigationManager.NavigateTo("/");
        }
    }

    private async Task LoadUsersAndRoles()
    {
        usersWithRoles = new();
        roles = RoleManager.Roles.Select(r => r.Name).ToList();

        var users = UserManager.Users.ToList();
        foreach (var user in users)
        {
            var role = (await UserManager.GetRolesAsync(user)).FirstOrDefault() ?? "No Role";
            usersWithRoles.Add(new UserWithRoles
                {
                    User = user,
                    Role = role
                });
        }

        StateHasChanged(); // Force re-render
    }

    private void EditUser(UserWithRoles user)
    {
        selectedUser = user.User;
        editModel = new EditUserModel
            {
                Id = user.User.Id,
                Email = user.User.Email,
                UserName = user.User.UserName,
                Role = user.Role
            };
    }

    private void CancelEdit()
    {
        selectedUser = null;
        editModel = new();
        StateHasChanged(); // Ensure UI update
    }

    private async Task HandleValidSubmit()
    {
        var user = await UserManager.FindByIdAsync(editModel.Id);
        if (user != null)
        {
            user.UserName = editModel.UserName;

            var updateResult = await UserManager.UpdateAsync(user);
            if (!updateResult.Succeeded)
            {
                return;
            }

            var oldRoles = await UserManager.GetRolesAsync(user);
            await UserManager.RemoveFromRolesAsync(user, oldRoles);
            await UserManager.AddToRoleAsync(user, editModel.Role);

            if (!string.IsNullOrWhiteSpace(editModel.NewPassword))
            {
                var token = await UserManager.GeneratePasswordResetTokenAsync(user);
                var passwordResult = await UserManager.ResetPasswordAsync(user, token, editModel.NewPassword);

                if (!passwordResult.Succeeded)
                {
                    return;
                }
            }

            await LoadUsersAndRoles();
            CancelEdit();
        }
    }

    private async Task DeleteUser(ApplicationUser user)
    {
        await UserManager.DeleteAsync(user);
        await LoadUsersAndRoles();
        CancelEdit();
    }
    private async Task ConfirmDelete(ApplicationUser user)
    {
        // show browser “confirm” dialog
        bool ok = await JSRuntime.InvokeAsync<bool>(
            "confirm",
            $"Are you sure you want to delete {user.UserName}?"
        );

        if (ok)
        {
            // if they clicked “OK”, run your delete logic
            await DeleteUser(user);
        }
    }

}
