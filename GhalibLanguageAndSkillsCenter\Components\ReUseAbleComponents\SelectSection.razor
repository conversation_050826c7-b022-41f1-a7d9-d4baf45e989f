﻿@inject IGenericRepository<SectionModel> SectionRepo
<select class="form-control" @onchange="OnSectionChanged">
    <option value="">-- Select Section --</option>
    @foreach (var sec in Sections)
    {
        <option value="@sec.SectionId">@sec.Name</option>
    }
</select>

@code {

    public List<SectionModel> Sections { get; set; } = new();

    [Parameter]
    public int SelectedSectionId { get; set; }

    [Parameter]
    public EventCallback<int> SelectedSectionIdChanged { get; set; }

    protected override async Task OnInitializedAsync()
    {
        Sections = (await SectionRepo.GetAllAsync("GetAllSections")).ToList();
    }

    private async Task OnSectionChanged(ChangeEventArgs e)
    {
        if (int.TryParse(e.Value?.ToString(), out var id))
        {
            SelectedSectionId = id;
            await SelectedSectionIdChanged.InvokeAsync(id);
        }
    }
}
