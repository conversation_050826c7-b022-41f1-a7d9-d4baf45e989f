﻿@page "/users"
@using Microsoft.AspNetCore.Identity
@using Microsoft.EntityFrameworkCore
@inject UserManager<ApplicationUser> UserManager
@inject NavigationManager Navigation
@inject IJSRuntime JS
@rendermode InteractiveServer
<h3>User Management</h3>
@attribute [Authorize(Roles = "Administrator")]
@if (!string.IsNullOrEmpty(AlertMessage))
{
    <div class="alert @AlertCssClass" role="alert">
        @AlertMessage
    </div>
}

@if (Users == null)
{
    <p><em>Loading users...</em></p>
}
else
{
    <table class="table table-bordered">
        <thead class="table-light">
            <tr>
                <th>Email</th>
                <th>New Email</th>
                <th>New Password</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var user in Users)
            {
                <tr>
                    <td>@user.Email</td>
                    <td>
                        <input class="form-control" @bind="user.NewEmail" />
                    </td>
                    <td>
                        <input class="form-control" @bind="user.NewPassword" type="password" />
                    </td>
                    <td>
                        <button class="btn btn-sm btn-primary me-2" @onclick="() => UpdateUser(user)">Update</button>
                        <button class="btn btn-sm btn-danger" @onclick="() => DeleteUser(user.Id)">Delete</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}

@code {
    // Editable user view model for binding
    public class EditableUser
    {
        public string Id { get; set; }
        public string Email { get; set; }
        public string NewEmail { get; set; }
        public string NewPassword { get; set; }
    }

    List<EditableUser> Users;
    string AlertMessage;
    string AlertCssClass;

    protected override async Task OnInitializedAsync()
    {
        await LoadUsers();
    }

    private async Task LoadUsers()
    {
        var userList = await UserManager.Users.ToListAsync();
        Users = userList.Select(u => new EditableUser
            {
                Id = u.Id,
                Email = u.Email,
                NewEmail = u.Email
            }).ToList();
    }

    private async Task UpdateUser(EditableUser user)
    {
        var identityUser = await UserManager.FindByIdAsync(user.Id);
        if (identityUser == null)
        {
            ShowAlert("User not found.", "alert-danger");
            return;
        }

        bool updated = false;

        if (user.NewEmail != identityUser.Email)
        {
            identityUser.Email = user.NewEmail;
            identityUser.UserName = user.NewEmail;
            var result = await UserManager.UpdateAsync(identityUser);
            if (!result.Succeeded)
            {
                ShowAlert("Failed to update email.", "alert-danger");
                return;
            }
            updated = true;
        }

        if (!string.IsNullOrWhiteSpace(user.NewPassword))
        {
            var token = await UserManager.GeneratePasswordResetTokenAsync(identityUser);
            var passResult = await UserManager.ResetPasswordAsync(identityUser, token, user.NewPassword);
            if (!passResult.Succeeded)
            {
                ShowAlert("Failed to update password.", "alert-danger");
                return;
            }
            updated = true;
        }

        if (updated)
        {
            ShowAlert("User updated successfully.", "alert-success");
        }
        else
        {
            ShowAlert("No changes made.", "alert-info");
        }

        await LoadUsers(); // reload updated data
    }

    private async Task DeleteUser(string userId)
    {
        var confirm = await JS.InvokeAsync<bool>("confirm", "Are you sure you want to delete this user?");
        if (!confirm) return;

        var user = await UserManager.FindByIdAsync(userId);
        if (user != null)
        {
            var result = await UserManager.DeleteAsync(user);
            if (result.Succeeded)
            {
                ShowAlert("User deleted.", "alert-success");
                await LoadUsers();
            }
            else
            {
                ShowAlert("Failed to delete user.", "alert-danger");
            }
        }
    }

    void ShowAlert(string message, string cssClass)
    {
        AlertMessage = message;
        AlertCssClass = cssClass;
    }
}
