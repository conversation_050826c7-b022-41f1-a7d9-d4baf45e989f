﻿@page "/Account/Register"
@layout GhalibLanguageAndSkillsCenter.Components.Layout.LoginAndRegisterLayout

@using System.ComponentModel.DataAnnotations
@using System.Text
@using System.Text.Encodings.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.WebUtilities
@using GhalibLanguageAndSkillsCenter.Data

@inject UserManager<ApplicationUser> UserManager
@inject IUserStore<ApplicationUser> UserStore
@inject SignInManager<ApplicationUser> SignInManager
@inject IEmailSender<ApplicationUser> EmailSender
@inject ILogger<Register> Logger
@inject NavigationManager NavigationManager
@inject IdentityRedirectManager RedirectManager
@inject RoleManager<IdentityRole> RoleManager
@attribute [Authorize(Roles = "Administrator")]
<PageTitle>Register</PageTitle>

<link href="css/auth.css" rel="stylesheet" />

<div class="auth-container">
    <div class="auth-card">
        <h1 class="auth-header">Create Your Account</h1>
        <StatusMessage Message="@Message" />

        <EditForm Model="Input" OnValidSubmit="RegisterUser" FormName="register">
            <DataAnnotationsValidator />
            <ValidationSummary class="auth-validation" />

            <div class="auth-form-group">
                <InputText @bind-Value="Input.Email"
                           id="Input.Email"
                           class="auth-input"
                           placeholder="Email" />
                <ValidationMessage For="() => Input.Email" class="auth-validation" />
            </div>

            <div class="auth-form-group">
                <InputText @bind-Value="Input.Password"
                           type="password"
                           id="Input.Password"
                           class="auth-input"
                           placeholder="Password" />
                <ValidationMessage For="() => Input.Password" class="auth-validation" />
            </div>

            <div class="auth-form-group">
                <InputText @bind-Value="Input.ConfirmPassword"
                           type="password"
                           id="Input.ConfirmPassword"
                           class="auth-input"
                           placeholder="Confirm Password" />
                <ValidationMessage For="() => Input.ConfirmPassword" class="auth-validation" />
            </div>

            <div class="auth-form-group">
                <InputSelect @bind-Value="Input.Role"
                             id="Input.Role"
                             class="auth-select">
                    <option value="">-- Choose a Role --</option>
                    @foreach (var role in AllRoles)
                    {
                        <option value="@role">@role</option>
                    }
                </InputSelect>
                <ValidationMessage For="() => Input.Role" class="auth-validation" />
            </div>

            <button type="submit" class="auth-btn">Register</button>

            <div class="auth-links">
                Already have an account?
                <a href="Account/Login">Log in</a>
            </div>
        </EditForm>
    </div>
</div>

@code {
    private IEnumerable<IdentityError>? identityErrors;

    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    private string? Message =>
      identityErrors is null
        ? null
        : $"Error: {string.Join(", ", identityErrors.Select(e => e.Description))}";

    private List<string> AllRoles { get; } = new()
    { "Administrator", "ComputerManager", "EnglishManager", "FinanceManager" };

    public async Task RegisterUser(EditContext _)
    {
        var user = Activator.CreateInstance<ApplicationUser>()!;
        await UserStore.SetUserNameAsync(user, Input.Email, CancellationToken.None);
        var emailStore = (IUserEmailStore<ApplicationUser>)UserStore;
        await emailStore.SetEmailAsync(user, Input.Email, CancellationToken.None);

        var result = await UserManager.CreateAsync(user, Input.Password);
        if (!result.Succeeded)
        {
            identityErrors = result.Errors;
            return;
        }

        if (!string.IsNullOrEmpty(Input.Role))
        {
            var roleResult = await UserManager.AddToRoleAsync(user, Input.Role);
            if (!roleResult.Succeeded)
            {
                identityErrors = roleResult.Errors;
                return;
            }
        }

        var code = await UserManager.GenerateEmailConfirmationTokenAsync(user);
        code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));
        var callbackUrl = NavigationManager.GetUriWithQueryParameters(
          NavigationManager.ToAbsoluteUri("Account/ConfirmEmail").AbsoluteUri,
          new Dictionary<string, object?>
              {
                  ["userId"] = await UserManager.GetUserIdAsync(user),
                  ["code"] = code
              });

        await EmailSender.SendConfirmationLinkAsync(
          user, Input.Email, HtmlEncoder.Default.Encode(callbackUrl));

        await SignInManager.SignInAsync(user, isPersistent: false);
        RedirectManager.RedirectTo("/");
    }

    private sealed class InputModel
    {
        [Required, EmailAddress] public string Email { get; set; } = "";
        [Required, DataType(DataType.Password)] public string Password { get; set; } = "";
        [Required, Compare("Password")] public string ConfirmPassword { get; set; } = "";
        [Required] public string Role { get; set; } = "";
    }
}
