﻿using System.ComponentModel.DataAnnotations;

namespace GhalibLanguageAndSkillsCenter.Models.Enrollment
{
    public class EnrollStudentModel
    {
        [Required(ErrorMessage = "Please enter  Name.")]
        public string Name { get; set; }
        [Required(ErrorMessage = "Please enter  LastName.")]
        public string LastName { get; set; }
        [Required(ErrorMessage = "Please enter  FatherName.")]
        public string FatherName { get; set; }
        [Required(ErrorMessage = "Please enter  Contact.")]
        public string Contact { get; set; }
        [Required(ErrorMessage = "Please enter  ProgramId.")]
        public int ProgramId { get; set; }
        [Required(ErrorMessage = "Please enter  SectionId.")]
        public int SectionId { get; set; }
        public string StudentIdNumber { get; set; }
        public int ShiftId { get; set; }
        [Required(ErrorMessage = "Please Add  Image.")]
        public string ProfileImage { get; set; }
        public DateTime EnrollmentDate { get; set; } = DateTime.Today;
    }
}
