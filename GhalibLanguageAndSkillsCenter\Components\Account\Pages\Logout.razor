﻿@page "/Account/Logout"
@inject NavigationManager NavigationManager
@inject SignInManager<ApplicationUser> SignInManager
@using Microsoft.AspNetCore.Identity
@using GhalibLanguageAndSkillsCenter.Data

<h3>Logging out...</h3>

@code {
    protected override async Task OnInitializedAsync()
    {
        await SignInManager.SignOutAsync();
        NavigationManager.NavigateTo("Account/Login", forceLoad: true);
    }
}
