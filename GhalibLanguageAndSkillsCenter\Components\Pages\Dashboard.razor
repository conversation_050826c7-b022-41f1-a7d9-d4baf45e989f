﻿@page "/"
@inject IGenericRepository<DashboardStatsModel> dashboardRepo
@inject IGenericRepository<ProgramModel> programRepo
@inject IGenericRepository<ActivityModel> activityRepo
@inject IGenericRepository<StudentPaymentModel> paymentRepo
@inject IGenericRepository<EnrollmentDetailsModel> enrollmentRepo
@attribute [Authorize]

<div class="dashboard-container fade-in">

    @if (isLoading)
    {
        <div class="text-center p-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3">Loading dashboard data...</p>
        </div>
    }
    else
    {
        <!-- Stats Cards Row -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card dashboard-card text-white bg-primary shadow">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">Active Programs</h5>
                                <p class="card-text display-6 fw-bold">@dashboardData.ActiveProgramsCount</p>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-book fs-1 opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card dashboard-card text-white bg-success shadow">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">Total Staff</h5>
                                <p class="card-text display-6 fw-bold">@dashboardData.StaffCount</p>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-people fs-1 opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card dashboard-card text-white bg-info shadow">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">Enrolled Students</h5>
                                <p class="card-text display-6 fw-bold">@dashboardData.EnrolledStudentsInActivePrograms</p>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-person-check fs-1 opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card dashboard-card text-white bg-warning shadow">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h5 class="card-title">This Month Revenue</h5>
                                <p class="card-text display-6 fw-bold">@currentMonthRevenue.ToString("N0")</p>
                            </div>
                            <div class="align-self-center">
                                <i class="bi bi-currency-dollar fs-1 opacity-75"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Program Deadlines -->
        <div class="card dashboard-card border-0 shadow mb-5">
            <div class="card-body">
                <h4 class="card-title mb-4">
                    <i class="bi bi-calendar-event me-2"></i>
                    Upcoming Program Deadlines
                </h4>
                <div class="row">
                    <div class="col-md-6">
                        <div class="border rounded p-3 bg-light">
                            <h5 class="text-primary">
                                <i class="bi bi-play-circle me-2"></i>
                                Starting in Next 2 Weeks
                            </h5>
                            @if (startingSoon.Any())
                            {
                                <ul class="list-group list-group-flush">
                                    @foreach (var p in startingSoon)
                                    {
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <span>@p.Name</span>
                                            <span class="badge bg-primary rounded-pill">@p.StartingDate.ToString("MMM dd")</span>
                                        </li>
                                    }
                                </ul>
                            }
                            else
                            {
                                <p class="text-muted fst-italic">None</p>
                            }
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="border rounded p-3 bg-light">
                            <h5 class="text-danger">
                                <i class="bi bi-stop-circle me-2"></i>
                                Ending in Next 2 Weeks
                            </h5>
                            @if (endingSoon.Any())
                            {
                                <ul class="list-group list-group-flush">
                                    @foreach (var p in endingSoon)
                                    {
                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                            <span>@p.Name</span>
                                            <span class="badge bg-danger rounded-pill">@p.EndingDate.ToString("MMM dd")</span>
                                        </li>
                                    }
                                </ul>
                            }
                            else
                            {
                                <p class="text-muted fst-italic">None</p>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Payments & Enrollments -->
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card dashboard-card shadow">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-credit-card me-2"></i>
                            Recent Payments
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <table class="table table-striped mb-0">
                            <thead class="table-dark">
                                <tr><th>Date</th><th>Student</th><th>Program</th><th>Amount</th></tr>
                            </thead>
                            <tbody>
                                @foreach (var pay in recentPayments)
                                {
                                    <tr>
                                        <td>@pay.PaymentDate.ToString("MMM dd")</td>
                                        <td>@pay.StudentName</td>
                                        <td>@pay.ProgramName</td>
                                        <td class="text-success fw-bold">@pay.PaidAmount.ToString("N0")</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="card dashboard-card shadow">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="bi bi-person-plus me-2"></i>
                            Recent Enrollments
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <table class="table table-striped mb-0">
                            <thead class="table-dark">
                                <tr><th>Date</th><th>Student</th><th>Program</th><th>Section</th></tr>
                            </thead>
                            <tbody>
                                @foreach (var e in recentEnrollments)
                                {
                                    <tr>
                                        <td>@e.EnrollmentDate.ToString("MMM dd")</td>
                                        <td>@e.StudentName @e.StudentLastName</td>
                                        <td>@e.ProgramName</td>
                                        <td>@e.SectionName</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    private bool isLoading = true;
    private DashboardStatsModel dashboardData = new();
    private List<ProgramModel> programs = new();
    private List<ProgramModel> startingSoon = new();
    private List<ProgramModel> endingSoon = new();
    private List<ActivityModel> recentActivities = new();
    private List<StudentPaymentModel> recentPayments = new();
    private List<EnrollmentDetailsModel> recentEnrollments = new();
    private decimal currentMonthRevenue = 0;

    protected override async Task OnInitializedAsync()
    {
        // Load all data except charts
        dashboardData = await dashboardRepo.GetByIdAsync("DashboardsProcedure", null);
        programs = (await programRepo.GetAllAsync("GetAllPrograms")).ToList();

        var today = DateTime.Today;
        var cutoff = today.AddDays(14);

        startingSoon = programs.Where(p => p.IsActive == 1 && p.StartingDate.Date >= today && p.StartingDate.Date <= cutoff)
                               .OrderBy(p => p.StartingDate)
                               .ToList();
        endingSoon = programs.Where(p => p.IsActive == 1 && p.EndingDate.Date >= today && p.EndingDate.Date <= cutoff)
                               .OrderBy(p => p.EndingDate)
                               .ToList();

        var allPayments = (await paymentRepo.GetAllAsync("GetAllStudentPayments")).ToList();
        recentPayments = allPayments.OrderByDescending(p => p.PaymentDate).Take(10).ToList();
        currentMonthRevenue = recentPayments.Where(p => p.PaymentDate.Month == DateTime.Now.Month && p.PaymentDate.Year == DateTime.Now.Year)
                                           .Sum(p => p.PaidAmount);

        var allEnrolls = (await enrollmentRepo.GetAllAsync("GetAllEnrollmentDetails")).ToList();
        recentEnrollments = allEnrolls.OrderByDescending(e => e.EnrollmentDate).Take(10).ToList();

        recentActivities = (await activityRepo.GetAllAsync("GetRecentActivities")).ToList();

        isLoading = false;
    }

    public class DashboardStatsModel
    {
        public int ActiveProgramsCount { get; set; }
        public int StaffCount { get; set; }
        public int EnrolledStudentsInActivePrograms { get; set; }
    }

    public class ProgramModel
    {
        public int ProgramId { get; set; }
        public string Name { get; set; } = string.Empty;
        public DateTime StartingDate { get; set; }
        public DateTime EndingDate { get; set; }
        public int IsActive { get; set; }
        public decimal Fee { get; set; }
    }

    public class ActivityModel
    {
        public string ActivityType { get; set; } = string.Empty;
        public DateTime ActivityDate { get; set; }
        public string Student { get; set; } = string.Empty;
        public string Program { get; set; } = string.Empty;
        public string ShiftName { get; set; } = string.Empty;
        public decimal AmountPaid { get; set; }
    }

    public class StudentPaymentModel
    {
        public int PaymentId { get; set; }
        public string StudentName { get; set; } = string.Empty;
        public string ProgramName { get; set; } = string.Empty;
        public decimal PaidAmount { get; set; }
        public DateTime PaymentDate { get; set; }
        public string BillNo { get; set; } = string.Empty;
        public string FatherName { get; set; } = string.Empty;
        public string StudentIdNumber { get; set; } = string.Empty;
        public decimal ProgramFee { get; set; }
        public decimal RemainingAmount { get; set; }
        public int Period { get; set; }
        public int StudentId { get; set; }
    }

    public class EnrollmentDetailsModel
    {
        public int EnrollmentId { get; set; }
        public string StudentName { get; set; } = string.Empty;
        public string StudentLastName { get; set; } = string.Empty;
        public string StudentFatherName { get; set; } = string.Empty;
        public int ProgramId { get; set; }
        public string ProgramName { get; set; } = string.Empty;
        public DateTime ProgramStartingDate { get; set; }
        public DateTime ProgramEndingDate { get; set; }
        public string CalculatedDuration { get; set; } = string.Empty;
        public int SectionId { get; set; }
        public string SectionName { get; set; } = string.Empty;
        public int ShiftId { get; set; }
        public string ShiftName { get; set; } = string.Empty;
        public DateTime EnrollmentDate { get; set; }
    }
}
