﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Authorization;

using GhalibLanguageAndSkillsCenter.Services;
using GhalibLanguageAndSkillsCenter.Models.Enrollment;
using GhalibLanguageAndSkillsCenter.Utilities;
using Microsoft.AspNetCore.Components.Forms;

namespace GhalibLanguageAndSkillsCenter.Components.Enrollment;

[Authorize]
public partial class EnrollStudent : ComponentBase
{
    [Inject] private IGenericRepository<object> repository { get; set; }
    [Inject] private IGenericRepository<GetIdFromDatabase> getidnumberRepo { get; set; }
    [Inject] private IGenericRepository<ProgramModel> programRepository { get; set; }

    private EnrollStudentModel enrollmentModel = new();
    private List<ProgramModel> programs = new();
    private string successMessage;
    private string errorMessage;
    private string enrollDate;
    private byte[] imageBytes;
    private GetIdFromDatabase generator = new();
    protected override async Task OnInitializedAsync()
    {
        programs = (await programRepository.GetAllAsync("GetAllPrograms")).ToList();
       
    }

    private void OnSectionChanged(int id)
    {
        enrollmentModel.SectionId = id;
    }
    private void OnProgramChange(int id)
    {
        enrollmentModel.ProgramId = id;
    }
    private void OnShiftChange(int id)
    {
        enrollmentModel.ShiftId = id;
    }
    private async Task HandleImageUpload(InputFileChangeEventArgs e)
    {
        var file = e.File;
        if (file == null)
            return;

        // 1) Compute a unique filename and the folder under wwwroot
        var fileName = $"{Guid.NewGuid()}{Path.GetExtension(file.Name)}";
        var imagesFolder = Path.Combine(Env.WebRootPath, "images");
        Directory.CreateDirectory(imagesFolder); // creates if missing

        // 2) Full disk path to wwwroot/images/<fileName>
        var absolutePath = Path.Combine(imagesFolder, fileName);

        // 3) Copy the uploaded file stream into that path
        await using (var fileStream = new FileStream(absolutePath, FileMode.Create))
        {
            // restrict max size as you see fit (e.g. 5 MB)
            await file.OpenReadStream(5 * 1024 * 1024).CopyToAsync(fileStream);
        }

        // 4) Store the *relative* URL (to use in the DB and for <img src=...> later)
        enrollmentModel.ProfileImage = $"/images/{fileName}";
    }

    private async Task HandleEnroll()
    {
        enrollmentModel.EnrollmentDate = PersianDateConverter.ToDateTime(enrollDate);
        if (enrollmentModel.ProgramId == 0 || enrollmentModel.SectionId == 0)
        {
            errorMessage = "Please select a Program and Section.";
            successMessage = null;
            return;
        }

        try
        {
            enrollmentModel.StudentIdNumber = await GenerateStudentIdAsync();
            await repository.AddAsync("RegisterStudentAndEnroll", new
            {
                enrollmentModel.Name,
                enrollmentModel.LastName,
                enrollmentModel.FatherName,
                enrollmentModel.Contact,
                enrollmentModel.ProgramId,
                enrollmentModel.SectionId,
                enrollmentModel.ShiftId,
                enrollmentModel.EnrollmentDate,
                enrollmentModel.StudentIdNumber,
                enrollmentModel.ProfileImage
            });

            successMessage = "Student successfully registered and enrolled!";
            errorMessage = null;
            enrollmentModel = new(); // Clear form
        }
        catch (Exception ex)
        {
            successMessage = null;
            errorMessage = $"An error occurred: {ex.Message}";
        }
    }
    private async Task<string> GenerateStudentIdAsync()
    {
        // 1) Define the fixed prefix
        const string prefix = "GLSC";

        // 2) Call the stored procedure via getidnumberRepo
        //    We expect exactly one row (or none) with a column StudentIdNumber.
        GetIdFromDatabase? result =
            await getidnumberRepo.GetByIdAsync(
                "GetLatestStudentIdByPrefix",
                new { Prefix = prefix }
            );

        // 3) Extract the latest ID string (if any)
        string? latestId = result?.StudentIdNumber;

        // 4) Compute the next numeric part
        int nextNumber = 1;
        if (!string.IsNullOrWhiteSpace(latestId)
            && latestId.Length > prefix.Length)
        {
            // e.g. latestId == "GLSC00042" → numberPart = "00042"
            string numberPart = latestId.Substring(prefix.Length);
            if (int.TryParse(numberPart, out int parsed))
            {
                nextNumber = parsed + 1;
            }
        }

        // 5) Return the new ID: "GLSC" + zero‐padded 5 digits
        //    e.g. 1 → "GLSC00001", 42 → "GLSC00042", etc.
        return $"{prefix}{nextNumber:D5}";
    }


    class GetIdFromDatabase
    {
        public string StudentIdNumber { get; set; }

    }
}
