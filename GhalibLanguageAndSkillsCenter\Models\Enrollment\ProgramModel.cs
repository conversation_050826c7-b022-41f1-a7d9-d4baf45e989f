﻿using System.ComponentModel.DataAnnotations;

namespace GhalibLanguageAndSkillsCenter.Models.Enrollment
{
    public class ProgramModel
    {
      
        public int ProgramId { get; set; }
        [Required(ErrorMessage = "Please enter  Name.")]
        public string Name { get; set; }
        [Required(ErrorMessage = "Please enter  Fee.")]
        public int Fee { get; set; }
        [Required(ErrorMessage = "Please enter  StartingDate.")]
        public DateTime StartingDate { get; set; } = DateTime.Today;
        [Required(ErrorMessage = "Please enter  EndingDate.")]
        public DateTime EndingDate { get; set; }= DateTime.Today;
        [Required(ErrorMessage = "Please enter  Department.")]
        public int DepartmentId { get; set; }

        public int Period { get; set; }

        public int IsActive { get; set; }
    }
}
