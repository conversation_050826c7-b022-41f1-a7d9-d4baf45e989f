﻿using System.ComponentModel.DataAnnotations;

namespace GhalibLanguageAndSkillsCenter.Models
{
    public class StudentPayment
    {
        public int StudentPaymentId { get; set; }

        [Required(ErrorMessage = "Please select a student")]
        [Range(1, int.MaxValue, ErrorMessage = "Please select a student")]
        public int StudentId { get; set; }

        [Required(ErrorMessage = "Please select a program")]
        [Range(1, int.MaxValue, ErrorMessage = "Please select a program")]
        public int ProgramId { get; set; }

        [Required(ErrorMessage = "Enter a payment amount")]
        public decimal PaidAmount { get; set; }

        public decimal DiscountPercentage { get; set; }
    }
}
