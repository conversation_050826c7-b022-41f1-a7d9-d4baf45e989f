﻿@page "/university"
@using System.ComponentModel.DataAnnotations
@using Microsoft.AspNetCore.Components.Forms
@inject IGenericRepository<UniversityModel> UniversityRepo
@inject IJSRuntime JS
@rendermode InteractiveServer
@attribute [Authorize]

<h3>@(isEditMode ? "Edit University" : "Add University")</h3>

<EditForm Model="@newUniversity" OnValidSubmit="AddOrUpdateUniversity">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="form-group mb-3">
        <label for="englishName">English Name</label>
        <InputText id="englishName" class="form-control" @bind-Value="newUniversity.EnglishName" />
        <ValidationMessage For="@(() => newUniversity.EnglishName)" />
    </div>

    <div class="form-group mb-3">
        <label for="dariName">Dari Name</label>
        <InputText id="dariName" class="form-control" @bind-Value="newUniversity.DariName" />
        <ValidationMessage For="@(() => newUniversity.DariName)" />
    </div>

    <div class="form-group mb-3">
        <label for="pashtoName">Pashto Name</label>
        <InputText id="pashtoName" class="form-control" @bind-Value="newUniversity.PashtoName" />
        <ValidationMessage For="@(() => newUniversity.PashtoName)" />
    </div>

    <button type="submit" class="btn btn-primary">
        @(isEditMode ? "Update University" : "Add University")
    </button>
    @if (isEditMode)
    {
        <button type="button" class="btn btn-secondary ms-2" @onclick="CancelEdit">Cancel</button>
    }
</EditForm>

<hr />

<h3>University List</h3>

@if (universities?.Any() == true)
{
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Id</th>
                <th>English Name</th>
                <th>Dari Name</th>
                <th>Pashto Name</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var uni in universities)
            {
                <tr>
                    <td>@uni.UniversityId</td>
                    <td>@uni.EnglishName</td>
                    <td>@uni.DariName</td>
                    <td>@uni.PashtoName</td>
                    <td>
                        <button class="btn btn-warning btn-sm" @onclick="() => EditUniversity(uni)">Edit</button>
                        <button class="btn btn-danger btn-sm ms-2" @onclick="() => ConfirmDelete(uni.UniversityId)">Delete</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
else
{
    <p>No universities available.</p>
}

@code {
    private UniversityModel newUniversity = new();
    private List<UniversityModel> universities = new();
    private bool isEditMode;

    protected override async Task OnInitializedAsync()
    {
        await LoadUniversities();
    }

    private async Task LoadUniversities()
    {
        universities = (await UniversityRepo.GetAllAsync("GetAllUniversities")).ToList();
    }

    private async Task AddOrUpdateUniversity()
    {
        try
        {
            if (isEditMode)
            {
                await UniversityRepo.UpdateAsync("UpdateUniversity", newUniversity);
                await ShowAlert("University updated successfully!");
            }
            else
            {
                await UniversityRepo.AddAsync("CreateUniversity", new
                {
                    EnglishName = newUniversity.EnglishName,
                    DariName = newUniversity.DariName,
                    PashtoName = newUniversity.PashtoName
                });
                await ShowAlert("University added successfully!");
            }

            CancelEdit();
            await LoadUniversities();
        }
        catch (Exception ex)
        {
            await ShowAlert($"An error occurred: {ex.Message}");
        }
    }

    private void EditUniversity(UniversityModel uni)
    {
        isEditMode = true;
        newUniversity = new UniversityModel
        {
            UniversityId = uni.UniversityId,
            EnglishName = uni.EnglishName,
            DariName = uni.DariName,
            PashtoName = uni.PashtoName
        };
    }

    private void CancelEdit()
    {
        isEditMode = false;
        newUniversity = new UniversityModel();
    }

    private async Task ConfirmDelete(int universityId)
    {
        var confirmed = await JS.InvokeAsync<bool>("confirm", "Are you sure you want to delete this university?");
        if (confirmed)
        {
            await DeleteUniversity(universityId);
        }
    }

    private async Task DeleteUniversity(int universityId)
    {
        try
        {
            await UniversityRepo.DeleteAsync("DeleteUniversity", new { UniversityId = universityId });
            await LoadUniversities();
            await ShowAlert("University deleted successfully!");
        }
        catch (Exception ex)
        {
            await ShowAlert($"An error occurred: {ex.Message}");
        }
    }

    private async Task ShowAlert(string message)
    {
        await JS.InvokeVoidAsync("alert", message);
    }

   
}
