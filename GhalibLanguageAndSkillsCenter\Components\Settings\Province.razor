﻿@page "/province"
@using Dapper
@using Microsoft.Data.SqlClient
@using System.ComponentModel.DataAnnotations
@using System.Data
@inject IConfiguration Configuration
@rendermode InteractiveServer
@attribute [Authorize]

<h3>Province</h3>

<EditForm Model="@newProvince" OnValidSubmit="HandleSubmit" FormName="province">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="form-group">
        <label for="provinceName">English Name</label>
        <InputText id="provinceName" class="form-control" @bind-Value="newProvince.Province" />
    </div>

    <div class="form-group">
        <label for="dariName">Dari Name</label>
        <InputText id="dariName" class="form-control" @bind-Value="newProvince.ProvinceDari" />
    </div>

    <div class="form-group">
        <label for="pashtoName">Pashto Name</label>
        <InputText id="pashtoName" class="form-control" @bind-Value="newProvince.ProvincePashto" />
    </div>

    <div class="form-group">
        <label for="countrySelect">Country</label>
        <InputSelect id="countrySelect" class="form-control" @bind-Value="newProvince.CountryId">
            <option value="0">-- Select Country --</option>
            @foreach (var country in countries)
            {
                <option value="@country.CountryId">@country.Country</option>
            }
        </InputSelect>
    </div>

    <button type="submit" class="btn btn-primary mt-2">
        @(isEditMode ? "Update Province" : "Add Province")
    </button>

    @if (isEditMode)
    {
        <button type="button" class="btn btn-secondary mt-2 ms-2" @onclick="CancelEdit">Cancel</button>
    }
</EditForm>

@if (isSuccess)
{
    <p class="alert alert-success mt-2">Operation completed successfully!</p>
}
@if (isError)
{
    <p class="alert alert-danger mt-2">Error: @errorMessage</p>
}

<hr />

<h3>Provinces List</h3>

@if (provinces?.Any() == true)
{
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Id</th>
                <th>English</th>
                <th>Dari</th>
                <th>Pashto</th>
                <th>Country</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var prov in provinces)
            {
                <tr>
                    <td>@prov.ProvinceId</td>
                    <td>@prov.Province</td>
                    <td>@prov.ProvinceDari</td>
                    <td>@prov.ProvincePashto</td>
                    <td>@countries.FirstOrDefault(c => c.CountryId == prov.CountryId)?.Country</td>
                    <td>
                        <button class="btn btn-warning btn-sm" @onclick="() => EditProvince(prov)">Edit</button>
                        <button class="btn btn-danger btn-sm ms-2" @onclick="() => ConfirmDelete(prov.ProvinceId)">Delete</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
else
{
    <p>No provinces found.</p>
}

@if (showDeleteConfirm)
{
    <div class="modal" style="display:block;">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" @onclick="CancelDelete"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this province?</p>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-danger" @onclick="DeleteProvince">Delete</button>
                    <button class="btn btn-secondary" @onclick="CancelDelete">Cancel</button>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private ProvinceModel newProvince = new ProvinceModel();
    private List<ProvinceModel> provinces = new List<ProvinceModel>();
    private List<CountryDto> countries = new List<CountryDto>();
    private bool isEditMode = false;
    private bool isSuccess = false;
    private bool isError = false;
    private string errorMessage = string.Empty;
    private bool showDeleteConfirm = false;
    private short provinceToDelete;
    private string connectionString;

    protected override void OnInitialized()
    {
        connectionString = Configuration.GetConnectionString("DefaultConnection");
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadCountries();
        await LoadProvinces();
    }

    private async Task LoadCountries()
    {
        using var conn = new SqlConnection(connectionString);
        countries = (await conn.QueryAsync<CountryDto>(
            "usp_GetAllCountries", commandType: CommandType.StoredProcedure))
            .ToList();
    }

    private async Task LoadProvinces()
    {
        using var conn = new SqlConnection(connectionString);
        provinces = (await conn.QueryAsync<ProvinceModel>(
            "usp_GetAllProvinces", commandType: CommandType.StoredProcedure))
            .ToList();
    }

    private async Task HandleSubmit()
    {
        try
        {
            using var conn = new SqlConnection(connectionString);
            var dp = new DynamicParameters();

            if (isEditMode)
            {
                dp.Add("@ProvinceId", newProvince.ProvinceId, DbType.Int16);
                dp.Add("@Province", newProvince.Province, DbType.String);
                dp.Add("@ProvinceDari", newProvince.ProvinceDari, DbType.String);
                dp.Add("@ProvincePashto", newProvince.ProvincePashto, DbType.String);
                dp.Add("@CountryId", newProvince.CountryId, DbType.Byte);
                await conn.ExecuteAsync("usp_UpdateProvince", dp, commandType: CommandType.StoredProcedure);
            }
            else
            {
                dp.Add("@Province", newProvince.Province, DbType.String);
                dp.Add("@ProvinceDari", newProvince.ProvinceDari, DbType.String);
                dp.Add("@ProvincePashto", newProvince.ProvincePashto, DbType.String);
                dp.Add("@CountryId", newProvince.CountryId, DbType.Byte);
                dp.Add("@NewProvinceId", dbType: DbType.Int16, direction: ParameterDirection.Output);
                await conn.ExecuteAsync("usp_CreateProvince", dp, commandType: CommandType.StoredProcedure);
                newProvince.ProvinceId = dp.Get<short>("@NewProvinceId");
            }

            isSuccess = true;
            isError = false;
            isEditMode = false;
            newProvince = new ProvinceModel();
            await LoadProvinces();
        }
        catch (Exception ex)
        {
            isError = true;
            errorMessage = ex.Message;
        }
    }

    private void EditProvince(ProvinceModel prov)
    {
        isEditMode = true;
        newProvince = prov;
    }

    private void ConfirmDelete(short id)
    {
        showDeleteConfirm = true;
        provinceToDelete = id;
    }

    private async Task DeleteProvince()
    {
        try
        {
            using var conn = new SqlConnection(connectionString);
            var dp = new DynamicParameters();
            dp.Add("@ProvinceId", provinceToDelete, DbType.Int16);
            await conn.ExecuteAsync("usp_DeleteProvince", dp, commandType: CommandType.StoredProcedure);
            showDeleteConfirm = false;
            isSuccess = true;
            await LoadProvinces();
        }
        catch (Exception ex)
        {
            isError = true;
            errorMessage = ex.Message;
        }
    }

    private void CancelEdit()
    {
        isEditMode = false;
        newProvince = new ProvinceModel();
    }

    private void CancelDelete()
    {
        showDeleteConfirm = false;
    }

    public class ProvinceModel
    {
        public short ProvinceId { get; set; }
        [Required(ErrorMessage = "Please enter English name.")] public string Province { get; set; }
        public string ProvinceDari { get; set; }
        public string ProvincePashto { get; set; }
        [Required(ErrorMessage = "Please select a country.")] public byte CountryId { get; set; }
    }

    public class CountryDto
    {
        public byte CountryId { get; set; }
        public string Country { get; set; }
    }
}
