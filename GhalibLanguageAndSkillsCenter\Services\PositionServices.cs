﻿using System.Data;
using Dapper;
using GhalibLanguageAndSkillsCenter.Data;
using GhalibLanguageAndSkillsCenter.Models.SettingsModels;

namespace GhalibLanguageAndSkillsCenter.Services
{
    public class PositionServices
    {
        private readonly DapperContext _context;

        public PositionServices(DapperContext context)
        {
            _context = context;
        }

        // Add a new position
        public async Task AddPosition(PositionModel position)
        {
            var query = "AddPosition";  
            using var connection = _context.CreateConnection();
            await connection.ExecuteAsync(query, new
            {
                EnglishName = position.EnglishName,
                DariName = position.DariName,
                PashtoName = position.PashtoName
            }, commandType: CommandType.StoredProcedure);
        }

        // Get all positions
        public async Task<IEnumerable<PositionModel>> GetAllPositions()
        {
            var query = "GetAllPositions";  // Ensure stored procedure 'GetAllPositions' exists
            using var connection = _context.CreateConnection();
            var positions = await connection.QueryAsync<PositionModel>(query, commandType: CommandType.StoredProcedure);
            return positions;
        }

        // Update an existing position
        public async Task UpdatePosition(PositionModel position)
        {
            var query = "UpdatePosition";  // Ensure stored procedure 'UpdatePosition' exists
            using var connection = _context.CreateConnection();
            await connection.ExecuteAsync(query, position, commandType: CommandType.StoredProcedure);
        }

        // Get position by Id
        public async Task<PositionModel> GetPositionById(int id)
        {
            var query = "GetPositionById";  // Ensure stored procedure 'GetPositionById' exists
            using var connection = _context.CreateConnection();
            var position = await connection.QueryFirstOrDefaultAsync<PositionModel>(query, new { PositionId = id }, commandType: CommandType.StoredProcedure);
            return position;
        }

        // Delete a position
        public async Task DeletePosition(int positionId)
        {
            var query = "DeletePosition";  // Ensure stored procedure 'DeletePosition' exists
            using var connection = _context.CreateConnection();
            await connection.ExecuteAsync(query, new { PositionId = positionId }, commandType: CommandType.StoredProcedure);
        }
    }
}
