﻿@inject IGenericRepository<ShiftModel> SectionRepo

<select class="form-control" @onchange="OnShiftChanged">
    <option value="">-- Select shift --</option>
    @foreach (var sec in Shifts)
    {
        <option value="@sec.ShiftId" selected="@(sec.ShiftId == SelectedShiftId)">
            @sec.Name
        </option>
    }
</select>

@code {
    public List<ShiftModel> Shifts { get; set; } = new();

    [Parameter]
    public int SelectedShiftId { get; set; }

    [Parameter]
    public EventCallback<int> SelectedShiftIdChanged { get; set; }

    protected override async Task OnInitializedAsync()
    {
        Shifts = (await SectionRepo.GetAllAsync("GetAllShifts")).ToList();
    }

    private async Task OnShiftChanged(ChangeEventArgs e)
    {
        if (int.TryParse(e.Value?.ToString(), out var id))
        {
            SelectedShiftId = id;
            await SelectedShiftIdChanged.InvokeAsync(id);
        }
    }
    public class ShiftModel
    {
        public int ShiftId { get; set; }
        public string Name { get; set; }

    }
}
