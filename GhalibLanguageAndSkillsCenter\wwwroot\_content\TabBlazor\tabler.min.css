﻿.nav-bordered .nav-item {
    margin-left: 1rem !important
}

.progress-lg {
    height: .8rem
}

.tabicon {
    border: none;
    background: none;
    outline: none;
    transition: transform .15s linear
}

    .tabicon:focus {
        outline: none;
        box-shadow: 0 0 0 .25rem #b9cce2;
        border-radius: 2px
    }

    .tabicon:hover {
        transform: scale(1.15)
    }

    .tabicon::-moz-focus-inner {
        border: 0
    }

.sr-only {
    height: 1px;
    overflow: hidden;
    position: absolute;
    width: 1px
}

.clickable-text {
    cursor: pointer;
    text-decoration: underline
}

.clickable-header {
    cursor: pointer
}

.badge.badge-default {
    text-transform: inherit
}

.badge.badge-md {
    font-size: 75%;
    font-weight: 400
}

.badge-list {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: -0.25rem !important;
    margin-right: -0.25rem
}

    .badge-list > * {
        margin: 0 .4rem .3rem 0 !important
    }

.modal-max {
    min-width: 99%
}

.dropdown-item.highlight {
    background-color: rgba(var(--tblr-secondary-rgb), 0.4)
}

.tree, .tree ul {
    margin: 0;
    padding: 0;
    list-style: none
}

.tree-node-expand-icon {
    flex: 0 0 auto
}

.tree-node-selected {
    border-color: #206bc4;
    border-style: solid;
    border-width: thin
}

.tree-node-no-drop {
    opacity: .5
}

.tree-node-aligned {
    width: 100%
}

.switch-icon {
    width: unset;
    height: unset
}

    .switch-icon svg {
        width: unset;
        height: unset
    }

    .switch-icon svg {
        width: unset;
        height: unset
    }

.dimmer {
    position: relative
}

    .dimmer .loader {
        display: none;
        margin: 0 auto;
        position: absolute;
        top: 35%;
        left: 0;
        right: 0;
        transform: translateY(-50%)
    }

    .dimmer.active .loader {
        display: block
    }

    .dimmer.active .dimmer-content {
        opacity: .5;
        pointer-events: none
    }

.arrow-down {
    width: 0;
    height: 0;
    border-left: 6px solid rgba(0,0,0,0);
    border-right: 6px solid rgba(0,0,0,0);
    border-top: 6px solid #495057;
    display: inline-block
}

.arrow-right {
    width: 0;
    height: 0;
    border-top: 6px solid rgba(0,0,0,0);
    border-bottom: 6px solid rgba(0,0,0,0);
    border-left: 6px solid #495057;
    display: inline-block
}

.grouped-table tr.grouping-row {
    text-align: left !important;
    font-weight: bold
}

.grouped-table tr td {
    padding-left: .5rem
}

.tabler-table tr:hover .row-action, .tabler-table tr:focus-within .row-action {
    transition: opacity .3s ease-in;
    opacity: 1
}

.tabler-table tr:focus-within {
    --tblr-table-accent-bg: var(--tblr-table-active-bg);
    color: var(--tblr-table-active-color)
}

.tabler-table .row-action {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    text-align: right;
    opacity: 0
}

    .tabler-table .row-action button {
        background-color: unset;
        border: none;
        padding-top: 0;
        padding-bottom: 0;
        padding-left: 0;
        padding-right: 0
    }

.tabler-table .row-action-edit {
    text-align: right
}

.tabler-table .sorting {
    opacity: .3
}

.flag {
    border: var(--tblr-border-size) var(--tblr-border-style) var(--tblr-border-color);
    border-radius: var(--tblr-border-radius)
}

.flag-xs {
    width: 1.5rem;
    height: 1.125rem
}

.flag-sm {
    width: 2rem;
    height: 1.5rem
}

.flag-md {
    width: 4rem;
    height: 3rem
}

.flag-lg {
    width: 5.5rem;
    height: 4.125rem
}

.flag-xl {
    width: 7rem;
    height: 5.25rem
}

.flag-2xl {
    width: 11rem;
    height: 8.25rem
}

.apexcharts-tooltip {
    color: var(--tblr-light) !important;
    background: var(--tblr-bg-surface-dark) !important;
    font-size: .765625rem !important;
    padding: 0 !important;
    box-shadow: none !important;
    border-width: 0px !important;
    border-radius: var(--tblr-border-radius) !important
}

.apexcharts-tooltip-title {
    background: 0 0 !important;
    border: 0 !important;
    margin: 0 !important;
    font-weight: var(--tblr-font-weight-bold);
    padding: .25rem .5rem !important
}

.apexcharts-tooltip-y-group {
    padding: 2px 0 !important
}

.apexcharts-tooltip-series-group {
    padding: .25rem !important
}

.apexcharts-tooltip-marker {
    width: 10px !important;
    height: 10px !important
}

.switch-icon-flip .switch-icon-a {
    opacity: 0
}

.switch-icon-flip.active .switch-icon-b {
    opacity: 0
}

.input-icon .form-control.is-valid, .input-icon .form-control.is-invalid {
    background-image: none !important
}

.icon.cursor-pointer {
    pointer-events: all
}
/*# sourceMappingURL=tabblazor.min.css.map */
