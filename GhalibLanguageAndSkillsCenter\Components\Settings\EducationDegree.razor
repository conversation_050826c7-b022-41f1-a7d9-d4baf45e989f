﻿@page "/education-degree"
@inject IGenericRepository<StaffDegreeModel> EducationDegreeRepo
@inject IJSRuntime JS
@using System.ComponentModel.DataAnnotations
@rendermode InteractiveServer
@attribute [Authorize]

<h3>@(isEditMode ? "Edit Education Degree" : "Add Education Degree")</h3>

<EditForm Model="@newDegree" OnValidSubmit="AddOrUpdateDegree">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="form-group mb-3">
        <label for="englishName">English Name</label>
        <InputText id="englishName" class="form-control" @bind-Value="newDegree.EnglishName" />
        <ValidationMessage For="@(() => newDegree.EnglishName)" />
    </div>

    <div class="form-group mb-3">
        <label for="dariName">Dari Name</label>
        <InputText id="dariName" class="form-control" @bind-Value="newDegree.DariName" />
        <ValidationMessage For="@(() => newDegree.DariName)" />
    </div>

    <div class="form-group mb-3">
        <label for="pashtoName">Pashto Name</label>
        <InputText id="pashtoName" class="form-control" @bind-Value="newDegree.PashtoName" />
        <ValidationMessage For="@(() => newDegree.PashtoName)" />
    </div>

    <button type="submit" class="btn btn-primary">
        @(isEditMode ? "Update Degree" : "Add Degree")
    </button>

    @if (isEditMode)
    {
        <button type="button" class="btn btn-secondary ms-2" @onclick="CancelEdit">Cancel</button>
    }
</EditForm>

<hr />

<h4>All Education Degrees</h4>

@if (degrees != null && degrees.Any())
{
    <table class="table table-striped">
        <thead>
            <tr>
                <th>ID</th>
                <th>English</th>
                <th>Dari</th>
                <th>Pashto</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var deg in degrees)
            {
                <tr>
                    <td>@deg.EducationDegreeId</td>
                    <td>@deg.EnglishName</td>
                    <td>@deg.DariName</td>
                    <td>@deg.PashtoName</td>
                    <td>
                        <button class="btn btn-warning btn-sm" @onclick="() => EditDegree(deg)">Edit</button>
                        <button class="btn btn-danger btn-sm ms-2" @onclick="() => ConfirmDelete(deg.EducationDegreeId)">Delete</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
else
{
    <p>No records found.</p>
}

@code {
    private StaffDegreeModel newDegree = new();
    private List<StaffDegreeModel> degrees = new();
    private bool isEditMode = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadDegrees();
    }

    private async Task LoadDegrees()
    {
        degrees = (await EducationDegreeRepo.GetAllAsync("GetAllEducationDegrees")).ToList();
    }

    private async Task AddOrUpdateDegree()
    {
        if (isEditMode)
        {
            await EducationDegreeRepo.UpdateAsync("UpdateEducationDegree", newDegree);
            await ShowAlert("Degree updated.");
        }
        else
        {
            await EducationDegreeRepo.AddAsync("CreateEducationDegree", new
            {
                EnglishName = newDegree.EnglishName,
                DariName    = newDegree.DariName,
                PashtoName  = newDegree.PashtoName
            });
            await ShowAlert("Degree added.");
        }

        newDegree = new();
        isEditMode = false;
        await LoadDegrees();
    }

    private void EditDegree(StaffDegreeModel degree)
    {
        isEditMode = true;
        newDegree = new StaffDegreeModel
        {
            EducationDegreeId = degree.EducationDegreeId,
            EnglishName       = degree.EnglishName,
            DariName          = degree.DariName,
            PashtoName        = degree.PashtoName
        };
    }

    private void CancelEdit()
    {
        newDegree = new();
        isEditMode = false;
    }

    private async Task ConfirmDelete(int id)
    {
        bool confirm = await JS.InvokeAsync<bool>("confirm", "Are you sure?");
        if (confirm)
        {
            await EducationDegreeRepo.DeleteAsync("DeleteEducationDegree", new { EducationDegreeId = id });
            await LoadDegrees();
            await ShowAlert("Deleted successfully.");
        }
    }

    private async Task ShowAlert(string message)
    {
        await JS.InvokeVoidAsync("alert", message);
    }

  
}
