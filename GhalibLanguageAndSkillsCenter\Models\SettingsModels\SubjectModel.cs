﻿using System.ComponentModel.DataAnnotations;

namespace GhalibLanguageAndSkillsCenter.Models.SettingsModels
{
    public class SubjectModel
    {
        public int SubjectId { get; set; }

        [Required]
        public string Name { get; set; }

        

        [Required]
        public int ProgramId { get; set; }

        [Required]
        public int StaffId { get; set; }

        // For display purposes (from joined results)
        public string ProgramName { get; set; }
        public string StaffFullName { get; set; }
    }
}
