﻿@page "/program"
@inject IGenericRepository<ProgramModel> programRepository
@inject IGenericRepository<DiscountModel> generaldiscountrepo
@inject IGenericRepository<DepartmentModel> departmentRepository
@inject IJSRuntime JS
@rendermode InteractiveServer
@attribute [Authorize]

@if (!showDiscountForm)
{
    <h3>Program</h3>

    <EditForm Model="@newProgram" OnValidSubmit="HandleSubmit" FormName="program">
        <DataAnnotationsValidator />
        <ValidationSummary />

        <div class="form-group">
            <label>Program Name</label>
            <InputText class="form-control" @bind-Value="newProgram.Name" />
        </div>

        <div class="form-group">
            <label>Fee</label>
            <InputNumber class="form-control" @bind-Value="newProgram.Fee" />
        </div>

        <div class="form-group">
            <label>Starting Date</label>
            <InputPersianDatePicker @bind-Value="startingDate" CssClass="form-control" />
        </div>

        <div class="form-group">
            <label>Ending Date</label>
            <InputPersianDatePicker @bind-Value="endingDate" CssClass="form-control" />
        </div>

        <div class="form-group">
            <label>Select Department</label>
            <InputSelect class="form-control" @bind-Value="newProgram.DepartmentId">
                <option value="">-- Select Department --</option>
                @foreach (var dept in departments)
                {
                    <option value="@dept.DepartmentId">@dept.EnglishName</option>
                }
            </InputSelect>
        </div>

        <button class="btn btn-primary mt-2" type="submit">
            @(isEditMode ? "Update Program" : "Add Program")
        </button>
        @if (isEditMode)
        {
            <button type="button" class="btn btn-secondary mt-2 ms-2" @onclick="CancelEdit">Cancel</button>
        }
    </EditForm>
}

<hr />

@if (showDiscountForm)
{
    <h4>@(discountModel.GeneralDiscountId == 0 ? "Add Discount to Program" : "Edit Discount")</h4>
    <EditForm Model="@discountModel" OnValidSubmit="SubmitDiscountForm">
        <DataAnnotationsValidator />
        <ValidationSummary />
        <div class="row mb-3">
        <div class="col-md-6">
            <label>Title</label>
            <InputText class="form-control" @bind-Value="discountModel.Title" />
        </div>

        <div class="col-md-6">
            <label>Discount Percentage</label>
            <InputNumber class="form-control" @bind-Value="discountModel.DiscountPercentage" />
        </div>
        </div>
        <button type="submit" class="btn btn-primary mt-2">
            @(discountModel.GeneralDiscountId == 0 ? "Add Discount" : "Update Discount")
        </button>
        @if (discountModel.GeneralDiscountId != 0)
        {
            <button type="button" class="btn btn-danger mt-2 ms-2" @onclick="DeleteDiscount">Delete Discount</button>
        }
        <button type="button" class="btn btn-secondary mt-2 ms-2" @onclick="CancelDiscountForm">Cancel</button>
    </EditForm>
}

<h3>Programs List</h3>

@if (programList.Any())
{
    <table class="table table-striped">
        <thead>
            <tr>
                <th>نام پروگرام</th>
                <th>فیس</th>
                <th>تاریخ شروع</th>
                <th>تاریخ ختم</th>
                <th>مدت زمان</th>
                <th>دیپارتمنت</th>
                <th>دوره</th>
                <th>وضعیت</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var p in programList)
            {
                <tr>
                    <td>@p.Name</td>
                    <td>@p.Fee</td>
                    <td>@PersianDateConverter.ToPersianString(p.StartingDate)</td>
                    <td>@PersianDateConverter.ToPersianString(p.EndingDate)</td>
                    <td>@CalculateDuration(p.StartingDate, p.EndingDate)</td>
                    <td>@departments.FirstOrDefault(d => d.DepartmentId == p.DepartmentId)?.EnglishName</td>
                    <td>@p.Period</td>
                    <td>@(p.IsActive == 1 ? "جریان دارد" : "پایان یافته است")</td>
                    <td>
                        <button class="btn btn-success btn-sm ms-2" @onclick="() => ShowDiscountForm(p.ProgramId)">Add/Edit Discount</button>
                        <button class="btn btn-warning btn-sm" @onclick="() => EditProgram(p)">Edit</button>
                        <button class="btn btn-danger btn-sm ms-2" @onclick="() => ConfirmDeleteProgram(p.ProgramId)">Delete</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
else
{
    <p>No programs found.</p>
}

@code {
    private ProgramModel newProgram = new();
    private List<ProgramModel> programList = new();
    private List<DepartmentModel> departments = new();
    private bool isEditMode = false;
    private bool showDiscountForm = false;
    private DiscountModel discountModel = new();
    private int selectedProgramIdForDiscount;
    private string? startingDate;
    private string? endingDate;

    public class DiscountModel
    {
        public int GeneralDiscountId { get; set; }
        public string Title { get; set; } = string.Empty;
        public decimal DiscountPercentage { get; set; }
        public int ProgramId { get; set; }
    }

    protected override async Task OnInitializedAsync()
    {
        departments = (await departmentRepository.GetAllAsync("AllDepartments")).ToList();
        await LoadPrograms();
    }

    private async Task LoadPrograms()
    {
        programList = (await programRepository.GetAllAsync("GetAllPrograms")).ToList();
    }

    private async Task ShowDiscountForm(int programId)
    {
        selectedProgramIdForDiscount = programId;
        // Try load existing discount for this program
        var existing = await generaldiscountrepo.GetByIdAsync("GetProgramDiscount", new { ProgramId = programId });
        if (existing != null && existing.GeneralDiscountId != 0)
        {
            discountModel = await generaldiscountrepo.GetByIdAsync("GetGeneralDiscountById", new { GeneralDiscountId = existing.GeneralDiscountId });
        }
        else
        {
            discountModel = new DiscountModel { ProgramId = programId };
        }
        showDiscountForm = true;
    }

    private void CancelDiscountForm()
    {
        showDiscountForm = false;
        discountModel    = new DiscountModel();
    }

    private async Task SubmitDiscountForm()
    {
        if (discountModel.GeneralDiscountId == 0)
        {
            // Add new discount
            await generaldiscountrepo.AddAsync("AddGeneralDiscount", new
            {
                Title               = discountModel.Title,
                DiscountPercentage  = discountModel.DiscountPercentage,
                ProgramId           = selectedProgramIdForDiscount
            });
        }
        else
        {
            // Update existing discount
            await generaldiscountrepo.UpdateAsync("UpdateOrDeleteGeneralDiscount", new
            {
                operation           = "update",
                Title               = discountModel.Title,
                DiscountPercentage  = discountModel.DiscountPercentage,
                GeneralDiscountId   = discountModel.GeneralDiscountId
            });
        }

        await CleanupAndReload();
    }

    private async Task DeleteDiscount()
    {
        var confirmed = await JS.InvokeAsync<bool>("confirm", "Are you sure you want to delete this discount?");
        if (!confirmed) return;

        await generaldiscountrepo.UpdateAsync("UpdateOrDeleteGeneralDiscount", new
        {
            operation           = "delete",
            Title               = (string?)null,
            DiscountPercentage  = (decimal?)null,
            GeneralDiscountId   = discountModel.GeneralDiscountId
        });

        await CleanupAndReload();
    }

    private async Task CleanupAndReload()
    {
        showDiscountForm = false;
        discountModel    = new DiscountModel();
        await LoadPrograms();
    }

    private async Task HandleSubmit()
    {
        newProgram.StartingDate = PersianDateConverter.ToDateTime(startingDate!);
        newProgram.EndingDate   = PersianDateConverter.ToDateTime(endingDate!);

        if (newProgram.DepartmentId == 0)
            return;

        if (isEditMode)
        {
            await programRepository.UpdateAsync("UpdateProgram", new
            {
                newProgram.ProgramId,
                newProgram.Name,
                newProgram.Fee,
                newProgram.StartingDate,
                newProgram.EndingDate,
                newProgram.DepartmentId
            });
        }
        else
        {
            await programRepository.AddAsync("AddProgram", new
            {
                newProgram.Name,
                newProgram.Fee,
                newProgram.StartingDate,
                newProgram.EndingDate,
                newProgram.DepartmentId
            });
        }

        newProgram = new();
        isEditMode = false;
        startingDate = endingDate = null;
        await LoadPrograms();
    }

    private void EditProgram(ProgramModel program)
    {
        isEditMode = true;
        newProgram = new ProgramModel
        {
            ProgramId    = program.ProgramId,
            Name         = program.Name,
            Fee          = program.Fee,
            DepartmentId = program.DepartmentId
        };
        startingDate = PersianDateConverter.ToPersianString(program.StartingDate);
        endingDate   = PersianDateConverter.ToPersianString(program.EndingDate);
    }

    private async Task ConfirmDeleteProgram(int id)
    {
        if (await JS.InvokeAsync<bool>("confirm", "Are you sure you want to delete this program?"))
        {
            await programRepository.DeleteAsync("DeleteProgram", new { ProgramId = id });
            await LoadPrograms();
        }
    }

    private void CancelEdit()
    {
        isEditMode = false;
        newProgram = new ProgramModel();
        startingDate = endingDate = null;
    }

    private int CalculateDuration(DateTime start, DateTime end)
        => (end - start).Days;
}
