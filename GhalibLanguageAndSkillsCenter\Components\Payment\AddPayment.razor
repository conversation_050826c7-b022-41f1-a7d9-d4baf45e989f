﻿@page "/add-payment"
@using GhalibLanguageAndSkillsCenter.Models.Payment
@using TabBlazor
@inject IGenericRepository<StudentModel> studentRepository
@inject IGenericRepository<ProgramModel> programRepository
@inject IGenericRepository<StudentPayment> paymentRepository
@inject IGenericRepository<DiscountModel> discountrepo
@inject IJSRuntime JS
@rendermode InteractiveServer
<div class="shadow-lg p-4 ">
<h3 class="mb-4">Add Student Payment</h3>
    @if (!String.IsNullOrEmpty(ErrorMessage))
    {
        <div class="alert alert-danger">@ErrorMessage</div>
    }
    @if (!String.IsNullOrEmpty(SuccessMessage))
    {
        <div class="alert alert-success">@SuccessMessage</div>
    }


<EditForm Model="paymentModel" OnValidSubmit="HandleValidSubmit" FormName="addpayment">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="mb-3">
        <label>Student</label>
            <ItemSelect TItem="StudentModel" TValue="int"
                        Items="students"
                        @bind-SelectedValue="paymentModel.StudentId"
                        SelectedTextExpression="student => student.Name"
                        ConvertExpression="student => student.StudentId"
                        Label="Select Student"
                        ShowSearch="true"
                        SearchMethod="SearchStudents"
                        SearchPlaceholderText="Search students..."
                        class="form-control"
                        Clearable="true"
                        
                        >
                <ListTemplate Context="student">
                    @student.Name  @student.LastName
                </ListTemplate>


            </ItemSelect>



    </div>

    <div class="mb-3">
        <label>Program</label>
        <InputSelect class="form-control" @bind-Value="paymentModel.ProgramId">
            <option value="">-- Select Program --</option>
            @foreach (var program in programs)
            {
                <option value="@program.ProgramId">@program.Name</option>
            }
        </InputSelect>
    </div>

    <div class="mb-3">
        <label>Payment Amount</label>
        <InputNumber class="form-control" @bind-Value="paymentModel.PaidAmount" />
    </div>

    <button class="btn btn-primary" type="submit">Submit Payment</button>
</EditForm>


</div>
@code {
    private StudentPayment paymentModel = new();
    private List<StudentModel> students = new();
    private List<ProgramModel> programs = new();
    private DiscountModel discount = new();
    private string ErrorMessage;
    private string SuccessMessage;

    protected override async Task OnInitializedAsync()
    {
        students = (await studentRepository.GetAllAsync("GetAllStudentDetails")).ToList();
        programs = (await programRepository.GetAllAsync("GetAllPrograms")).ToList();
        paymentModel = new StudentPayment(); // Reset the payment model
    }
    private IEnumerable<StudentModel> SearchStudents(string searchText)
    {
        return students.Where(student => student.Name.Contains(searchText, StringComparison.InvariantCultureIgnoreCase));
    }


    private async Task HandleValidSubmit()
    {
        ErrorMessage = null;
        SuccessMessage = null;
    
        discount = await discountrepo.GetByIdAsync(
            "GetStudentDiscountByProgramAndStudent",
            new { paymentModel.ProgramId, paymentModel.StudentId });

        if (discount != null)
        {
            bool keepDiscount = await JS.InvokeAsync<bool>("confirm",
                $"Student has a discount of {discount.DiscountPercentage}% " +
                $"given on {discount.Created_At:yyyy-MM-dd}.\n" +
                $"Do you want to apply this discount?");

            if (keepDiscount)
            {
                paymentModel.DiscountPercentage = discount.DiscountPercentage; // Pass discount to DB
            }
            else
            {
                await discountrepo.DeleteAsync("DeleteStudentDiscount", new
                {
                    StudentDiscountId = discount.StudentDiscountId
                });
                paymentModel.DiscountPercentage = 0; // Or leave null if not required
            }
        }

        // Proceed to add the payment
        try
        {
            // call and capture outputs
            var dp = await paymentRepository.AddAsyncAndGetOutpot(
                "AddStudentPayment",
                new
                {
                    StudentId = paymentModel.StudentId,
                    ProgramId = paymentModel.ProgramId,
                    PaidAmount = paymentModel.PaidAmount,
                    DiscountPercentage = paymentModel.DiscountPercentage
                }
            );

            var returnCode = dp.Get<int>("ReturnCode");
            var remainingAmount = dp.Get<decimal>("RemainingAmount");

            if (returnCode == 1)
            {
                ErrorMessage = $"Cannot process payment: student only owes {remainingAmount} more.";
            }
            else
            {
                SuccessMessage = "Payment recorded successfully.";
                paymentModel = new StudentPayment();
            }
        }
        catch (Exception ex)
        {
            ErrorMessage = "An unexpected error occurred. Please try again.";
            Console.Error.WriteLine(ex);
        }

    }
}
