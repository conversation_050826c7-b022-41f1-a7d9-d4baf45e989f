﻿using GhalibLanguageAndSkillsCenter.Components.Account.Pages.Manage;
using GhalibLanguageAndSkillsCenter.Models.Enrollment;
using GhalibLanguageAndSkillsCenter.Models.SettingsModels;
using GhalibLanguageAndSkillsCenter.Services;
using GhalibLanguageAndSkillsCenter.Utilities;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using TabBlazor;
using TabBlazor.Services;

namespace GhalibLanguageAndSkillsCenter.Components.Enrollment
{
    public partial class EnrollmentDetails : ComponentBase
    {
        private List<EnrollmentDetailsModel> enrollmentDetails = new();
        private List<EnrollmentDetailsModel> AllenrollmentDetails = new();
        private List<ProgramModel> programs = new();
        private List<SectionModel> sections = new();
        private List<ShiftModel> shifts = new();
        private string FilterDateStart;
        private string FilterDateEnd;

        private Table<EnrollmentDetailsModel> table;





        protected override async Task OnInitializedAsync()
        {
            await LoadEnrollmentDetails();
        }

        private async Task LoadEnrollmentDetails()
        {
            enrollmentDetails = (await repository.GetAllAsync("GetEnrollmentDetails")).ToList();
            AllenrollmentDetails = enrollmentDetails.ToList();

            programs = (await programRepository.GetAllAsync("GetAllPrograms")).ToList();
            sections = (await sectionRepository.GetAllAsync("GetAllSections")).ToList();
            shifts = (await shiftRepository.GetAllAsync("GetAllShifts")).ToList();
        }

        private async Task HandleRowUpdated(EnrollmentDetailsModel detail)
        {
            try
            {
                Console.WriteLine("Row update triggered");
                Console.WriteLine($"EnrollmentId: {detail.EnrollmentId}, ProgramId: {detail.ProgramId}, SectionId: {detail.SectionId}, ShiftId: {detail.ShiftId}");

                await repository.UpdateAsync("EditEnrollment", new
                {
                    detail.EnrollmentId,
                    detail.ProgramId,
                    detail.SectionId,
                    detail.ShiftId
                });
                await table.CloseEdit();
                await LoadEnrollmentDetails();

                await ToastService.AddToastAsync(new ToastModel
                {
                    Title = "Success",
                    SubTitle = "Updated",
                    Message = "Student Enrollment Updated",
                    Options = {Position = ToastPosition.BottomEnd}
                    
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine("Exception during HandleRowUpdated:");
                Console.WriteLine(ex.ToString());

                await ToastService.AddToastAsync(new ToastModel
                {
                    Title = "Error",
                    Message = $"Update failed: {ex.Message}",
                    
                });
            }
        }

      private  void TodaysEnrollment()
        {
            if(enrollmentDetails != null)
            {
                enrollmentDetails = enrollmentDetails.Where(e=> e.EnrollmentDate == DateTime.Today).ToList();
            }
        }
        private void FilterByStartingDate()
        {
            if (!string.IsNullOrEmpty(FilterDateStart))
            {
                if (!string.IsNullOrEmpty(FilterDateEnd))
                {
                    FilterByEndingDate();
                    return;
                }

                var startDate = PersianDateConverter.ToDateTime(FilterDateStart).Date;
                enrollmentDetails = AllenrollmentDetails
                    .Where(e => e.EnrollmentDate.Date == startDate)
                    .ToList();
            }
        }


        private void FilterByEndingDate()
        {
            if (!string.IsNullOrEmpty(FilterDateStart) &&
                !string.IsNullOrEmpty(FilterDateEnd))
            {
                var startDate = PersianDateConverter
                                    .ToDateTime(FilterDateStart)
                                    .Date;
                var endDate = PersianDateConverter
                                  .ToDateTime(FilterDateEnd)
                                  .Date;

                enrollmentDetails = AllenrollmentDetails
                    .Where(e => e.EnrollmentDate.Date >= startDate
                             && e.EnrollmentDate.Date <= endDate)
                    .ToList();
            }
        }


        private void ClearFilters()
        {
            FilterDateStart =null;
            FilterDateEnd = null;
            enrollmentDetails = AllenrollmentDetails;

        }

        private async Task DeleteEnrollment(int enrollmentId)
        {
            bool confirmed = await JS.InvokeAsync<bool>("confirm", "Are you sure you want to delete this enrollment?");
            if (confirmed)
            {
                await repository.DeleteAsync("DeleteEnrollment", new { EnrollmentId = enrollmentId });
                await LoadEnrollmentDetails();

                await ToastService.AddToastAsync(new ToastModel
                {
                    Title = "Deleted",
                    Message = "Enrollment deleted successfully.",
                    Options = new ToastOptions { Position = ToastPosition.BottomEnd }
                });
            }
        }

        public class ShiftModel
        {
            public int ShiftId { get; set; }
            public string Name { get; set; }
        }
    }
}
