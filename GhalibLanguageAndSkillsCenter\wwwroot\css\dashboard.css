﻿/* General container fade-in */
.dashboard-container {
    animation: dashboard-fadeIn 0.6s ease-in-out;
}

/* Card enhancements */
.dashboard-card {
    border-radius: 1rem;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: default;
}

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 24px rgba(0, 0, 0, 0.25);
    }

/* Custom badge animation */
.dashboard-badge {
    font-size: 0.9rem;
    padding: 0.4em 0.8em;
    animation: dashboard-bounce 0.9s ease;
}

/* Table row hover */
.dashboard-table-row:hover {
    background-color: #f8f9fa;
    transition: background-color 0.3s ease;
}

/* List item spacing & hover */
.dashboard-deadline-item {
    transition: background-color 0.3s ease;
}

    .dashboard-deadline-item:hover {
        background-color: #f0f0f0;
    }

/* Fade-in animation */
@keyframes dashboard-fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Bounce for badges */
@keyframes dashboard-bounce {
    0% {
        transform: scale(0.95);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}
