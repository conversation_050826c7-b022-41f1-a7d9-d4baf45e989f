﻿using System.Globalization;

namespace GhalibLanguageAndSkillsCenter.Utilities
{
    public static class AfghanCalendarCulture
    {
        // Full Afghan month names
        public static readonly string[] AfghanMonthNames =
        {
            "حمل", "ثور", "جوزا", "سرطان", "اسد", "سنبله",
            "میزان", "عقرب", "قوس", "جدی", "دلو", "حوت"
        };

        // Optionally, abbreviated names (if needed)
        public static readonly string[] AfghanAbbreviatedMonthNames =
        {
            "حم", "ثر", "جو", "سر", "اس", "سن",
            "می", "عق", "قو", "جد", "دل", "حو"
        };

        // A method that returns a custom CultureInfo with Afghan month names
        public static CultureInfo GetAfghanCulture()
        {
            var culture = new CultureInfo("fa-IR", false); // Based on Persian
            var dtf = culture.DateTimeFormat;

            dtf.MonthNames = AfghanMonthNames.Concat(new[] { "" }).ToArray(); // 13th blank required
            dtf.AbbreviatedMonthNames = AfghanAbbreviatedMonthNames.Concat(new[] { "" }).ToArray();

            return culture;
        }
    }
}