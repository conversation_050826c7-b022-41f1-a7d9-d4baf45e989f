﻿using Blazorise.Snackbar;

namespace GhalibLanguageAndSkillsCenter.Services
{
    public class SnackbarBridge
    {
        public Snackbar Success { get; set; } = default!;
        public Snackbar Error { get; set; } = default!;
        public string Message { get; set; } = "";

        public void ShowSuccess(string message)
        {
            Message = message;
            Success.Show();
        }
        public void ShowError(string message)
        {
            Message = message;
            Error.Show();
        }
    }
}
