﻿using Blazorise.Snackbar;

namespace GhalibLanguageAndSkillsCenter.Services
{
    public class SnackbarBridge
    {
        public Snackbar Success { get; set; } = default!;
        public Snackbar Error { get; set; } = default!;
        public string Message { get; set; } = "";

        public void ShowSuccess(string message)
        {
            if (string.IsNullOrEmpty(message)) return;

            Message = message;
            if (Success != null)
            {
                Success.Show();
            }
            else
            {
                // Fallback: log to console if snackbar not available
                Console.WriteLine($"Success: {message}");
            }
        }

        public void ShowError(string message)
        {
            if (string.IsNullOrEmpty(message)) return;

            Message = message;
            if (Error != null)
            {
                Error.Show();
            }
            else
            {
                // Fallback: log to console if snackbar not available
                Console.WriteLine($"Error: {message}");
            }
        }
    }
}
