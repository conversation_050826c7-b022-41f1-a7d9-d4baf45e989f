﻿using System.Data;
using Dapper;
using GhalibLanguageAndSkillsCenter.Data;
using GhalibLanguageAndSkillsCenter.Models.SettingsModels;
namespace GhalibLanguageAndSkillsCenter.Services
{
    public class DepartmentServices
    {
        private readonly DapperContext _context;

        public DepartmentServices(DapperContext context)
        {
            _context = context;
        }

        // CREATE
        public async Task AddDepartment(string EnglishName, string DariName, string PashtoName)
        {
            var query = "AddDepartment";
            using var connection = _context.CreateConnection();
            await connection.ExecuteAsync(query, new
            {
                EnglishName,
                DariName,
                PashtoName
            }, commandType: CommandType.StoredProcedure);
        }

        // READ - Get All Departments
        public async Task<IEnumerable<DepartmentModel>> GetAllDepartments()
        {
            var query = "AllDepartments"; // your stored procedure for getting all
            using var connection = _context.CreateConnection();
            var departments = await connection.QueryAsync<DepartmentModel>(query, commandType: CommandType.StoredProcedure);
            return departments;
        }

        // READ - Get Department by Id
        public async Task<DepartmentModel> GetDepartmentById(int departmentId)
        {
            var query = "GetDepartmentById";
            using var connection = _context.CreateConnection();
            var department = await connection.QueryFirstOrDefaultAsync<DepartmentModel>(query, new
            {
                DepartmentId = departmentId
            }, commandType: CommandType.StoredProcedure);

            return department;
        }

        // UPDATE
        public async Task UpdateDepartment(int departmentId, string EnglishName, string DariName, string PashtoName)
        {
            var query = "UpdateDepartment";
            using var connection = _context.CreateConnection();
            await connection.ExecuteAsync(query, new
            {
                DepartmentId = departmentId,
                EnglishName,
                DariName,
                PashtoName
            }, commandType: CommandType.StoredProcedure);
        }

        // DELETE
        public async Task DeleteDepartment(int departmentId)
        {
            var query = "DeleteDepartment";
            using var connection = _context.CreateConnection();
            await connection.ExecuteAsync(query, new
            {
                DepartmentId = departmentId
            }, commandType: CommandType.StoredProcedure);
        }
    }

    // You can define a simple model for Department
  
}
