﻿@page "/reports"
@inject IJSRuntime JS

@inject IGenericRepository<StudentPaymentDetailModel> paymentRepository
@inject IJSRuntime JS
@rendermode InteractiveServer
@attribute [Authorize]

<div class="card shadow-sm border-0 mb-4 p-3" style="background-color: #f8f9fa; border-radius: 1rem;">
    <div class="row g-3 align-items-end">

        <div class="col-md-2 d-grid">
            <button class="btn btn-dark" @onclick="TodaysPayment">
                📅 پرداختی امروز
            </button>
        </div>

        <div class="col-md-3">
            <label class="form-label fw-bold text-secondary">برنامه درسی</label>
            <GhalibLanguageAndSkillsCenter.Components.ReUseAbleComponents.SelectProgram SelectedProgramId="SelectedProgramId"
                                                                                        SelectedProgramIdChanged="HandleProgramIdChanged" />
        </div>

        <div class="col-md-2">
            <label class="form-label fw-bold text-secondary">از تاریخ</label>
            <InputPersianDatePicker CssClass="form-control shadow-sm"
                                    @bind-Value="StartDate"
                                    OnChange="HandleStartingDate"
                                    MinDateSetOnToday="false"
                                    Placeholder="-----انتخاب تاریخ-----"
                                    Theme="PickerTheme.Cheerup" />
        </div>

        <div class="col-md-2">
            <label class="form-label fw-bold text-secondary">تا تاریخ</label>
            <InputPersianDatePicker CssClass="form-control shadow-sm"
                                    @bind-Value="EndDate"
                                    OnChange="HandleEndingDate"
                                    MinDateSetOnToday="false"
                                    Placeholder="-----انتخاب تاریخ-----"
                                    Theme="PickerTheme.Cheerup" />
        </div>

        <div class="col-md-2 d-grid">
            <button class="btn btn-outline-danger" @onclick="RemoveFilters">
                ❌ پاک کردن فلتر
            </button>
        </div>

    </div>
</div>

@if (paymentDetails == null)
{
    <p>در حال بارگذاری داده‌ها...</p>
}
else if (!paymentDetails.Any())
{
    <p>هیچ رکورد پرداختی یافت نشد.</p>
}
else
{
    

    <table class="table table-striped custom-table">
        <thead>
            <tr>
                <th>نام شاگرد</th>
                <th>نام برنامه</th>
                <th>فیس</th>
                <th>تخفیف (%)</th>
                <th>تخفیف عمومی (%)</th>
                <th>قابل پرداخت</th>
                <th>مبلغ پرداختی</th>
                <th>تاریخ پرداخت</th>
                <th>قسط</th>
                <th>شماره قبض</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in paymentDetails)
            {
                <tr>
                    <td>@item.StudentName</td>
                    <td>@item.Name</td>
                    <td>@item.OriginalFee.ToString("N0")</td>
                    <td>@item.DiscountPercentage.ToString("N2")</td>
                    <td>@item.GeneralDiscountPercentage.ToString("N2")</td>
                    <td>@item.FinalFee.ToString("N0")</td>
                    <td>@item.PaidAmount.ToString("N0")</td>
                    <td>@PersianDateConverter.ToPersianString(item.PaymentDate)</td>
                    <td>@item.Installment</td>
                    <td>@item.BillNo</td>
                </tr>
            }
        </tbody>
    </table>

}

@code {

    private List<StudentPaymentDetailModel> allPaymentDetails = new();
    private List<StudentPaymentDetailModel>? paymentDetails;
    private int SelectedProgramId;


    private string StartDate = string.Empty;
    private string EndDate = string.Empty;

    protected override async Task OnInitializedAsync()
    {

        allPaymentDetails = (await paymentRepository.GetAllAsync("GetStudentPaymentDetails")).ToList();


        paymentDetails = allPaymentDetails.ToList();
    }
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JS.InvokeVoidAsync("replaceMonthNames");
        }
    }

    public void HandleProgramIdChanged(int id)
    {
        SelectedProgramId = id;
        paymentDetails = paymentDetails.Where(p => p.ProgramId == SelectedProgramId).ToList();
    }

    private void RemoveFilters()
    {
        paymentDetails = allPaymentDetails;
        StartDate = string.Empty;
        EndDate = string.Empty;
    }


    private void TodaysPayment()
    {
   
        var today = DateTime.Today;
        paymentDetails = allPaymentDetails
            .Where(p => p.PaymentDate.Date == today)
            .ToList();
    }

   
    private void HandleStartingDate()
    {

        ApplyDateFilter();
    }

  
    private void HandleEndingDate()
    {
        ApplyDateFilter();
    }

   
    private void ApplyDateFilter()
    {

        var filtered = allPaymentDetails.AsEnumerable();

   
        if (!string.IsNullOrWhiteSpace(StartDate) && string.IsNullOrWhiteSpace(EndDate))
        {
            var startDt = PersianDateConverter.ToDateTime(StartDate).Date;
            filtered = filtered.Where(p => p.PaymentDate.Date == startDt);
        }
        
        else if (!string.IsNullOrWhiteSpace(StartDate) && !string.IsNullOrWhiteSpace(EndDate))
        {
            var startDt = PersianDateConverter.ToDateTime(StartDate).Date;
            var endDt = PersianDateConverter.ToDateTime(EndDate).Date;

            if (endDt < startDt)
            {
              
                var tmp = startDt;
                startDt = endDt;
                endDt = tmp;
            }

            filtered = filtered
                .Where(p => p.PaymentDate.Date >= startDt && p.PaymentDate.Date <= endDt);
        }
       
        else if (string.IsNullOrWhiteSpace(StartDate) && !string.IsNullOrWhiteSpace(EndDate))
        {
            var endDt = PersianDateConverter.ToDateTime(EndDate).Date;
            filtered = filtered.Where(p => p.PaymentDate.Date == endDt);
        }
      
        paymentDetails = filtered.ToList();
    }


 
    public class StudentPaymentDetailModel
    {
        public int StudentPaymentDetailId { get; set; }
        public int StudentPaymentSummaryId { get; set; }
        public int StudentId { get; set; }
        public string StudentName { get; set; } = string.Empty;
        public int ProgramId { get; set; }
        public string Name { get; set; } = string.Empty;    
        public decimal OriginalFee { get; set; }
        public decimal DiscountPercentage { get; set; }
        public decimal TotalPaid { get; set; }
        public decimal GeneralDiscountPercentage { get; set; }
        public decimal FinalFee { get; set; }
        public decimal PaidAmount { get; set; }
        public DateTime PaymentDate { get; set; }
        public int Installment { get; set; }
        public string BillNo { get; set; } = string.Empty;
    }
}
<style>
    .custom-table {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

        .custom-table thead {
            background: linear-gradient(to right, #4e54c8, #8f94fb);
            color: white;
            font-weight: 600;
            text-align: center;
        }

        .custom-table tbody tr {
            transition: background-color 0.3s ease;
        }

            .custom-table tbody tr:hover {
                background-color: #f1f1f1;
            }

        .custom-table td,
        .custom-table th {
            vertical-align: middle;
            text-align: center;
        }

        .custom-table td {
            font-size: 0.95rem;
        }

        .custom-table th {
            font-size: 1rem;
            letter-spacing: 0.5px;
        }
</style>