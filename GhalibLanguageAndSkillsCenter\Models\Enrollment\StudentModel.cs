﻿using System.ComponentModel.DataAnnotations;

namespace GhalibLanguageAndSkillsCenter.Models.Enrollment
{
    public class StudentModel
    {
        public int StudentId { get; set; }
        public int StudentDetailsId {  get; set; }
        [Required(ErrorMessage = "Please enter  Name.")]
        public string Name { get; set; }
        [Required(ErrorMessage = "Please enter  LastName.")]
        public string LastName { get; set; }
        [Required(ErrorMessage = "Please enter  FatherName.")]
        public string FatherName { get; set; }
        [Required(ErrorMessage = "Please enter  Contact.")]
        public string Contact { get; set; }

        public int Age { get; set; }

        public byte[] ProfileImage { get; set; }
        public string TazkeraNumber { get; set; }

        public string Address { get; set; }
    }
}
