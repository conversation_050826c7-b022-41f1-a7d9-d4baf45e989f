﻿@page "/Position"
@inject IGenericRepository<PositionModel> PositionRepo
@attribute [Authorize]
@rendermode InteractiveServer

<h3>@(isEditMode ? "Edit" : "Add") Position</h3>

<EditForm Model="@newPosition" OnValidSubmit="HandleValidSubmit" FormName="position">
    <DataAnnotationsValidator />
    <ValidationSummary />

    <div class="form-group">
        <label for="englishName">English Name</label>
        <InputText id="englishName" class="form-control" @bind-Value="newPosition.EnglishName" />
    </div>

    <div class="form-group">
        <label for="dariName">Dari Name</label>
        <InputText id="dariName" class="form-control" @bind-Value="newPosition.DariName" />
    </div>

    <div class="form-group">
        <label for="pashtoName">Pashto Name</label>
        <InputText id="pashtoName" class="form-control" @bind-Value="newPosition.PashtoName" />
    </div>

    <button class="btn btn-primary mt-2" type="submit">
        @(isEditMode ? "Update Position" : "Add Position")
    </button>

    @if (isError)
    {
        <p class="alert alert-danger mt-2">Error: @errorMessage</p>
    }

    @if (isSuccess)
    {
        <p class="alert alert-success mt-2">Operation completed successfully!</p>
    }
</EditForm>

<hr />

<h3>Positions List</h3>

@if (positions != null && positions.Any())
{
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Id</th>
                <th>English Name</th>
                <th>Dari Name</th>
                <th>Pashto Name</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var pos in positions)
            {
                <tr>
                    <td>@pos.PositionId</td>
                    <td>@pos.EnglishName</td>
                    <td>@pos.DariName</td>
                    <td>@pos.PashtoName</td>
                    <td>
                        <button class="btn btn-warning btn-sm" @onclick="() => EditPosition(pos)">Edit</button>
                        <button class="btn btn-danger btn-sm ms-2" @onclick="() => ConfirmDelete(pos.PositionId)">Delete</button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
else
{
    <p>No positions available.</p>
}
@if (showDeleteConfirmation)
{
    <div class="modal show d-block" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this position?</p>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-danger" @onclick="DeletePosition">Delete</button>
                    <button class="btn btn-secondary" @onclick="CancelDelete">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal-backdrop fade show"></div>
}



@code {
    private PositionModel newPosition = new PositionModel();
    private IEnumerable<PositionModel> positions = new List<PositionModel>();

    private bool isSuccess = false;
    private bool isError = false;
    private string errorMessage = "";
    private bool isEditMode = false;
    private bool showDeleteConfirmation = false;
    private int positionIdToDelete;

    protected override async Task OnInitializedAsync()
    {
        await LoadPositions();
    }

    private async Task LoadPositions()
    {
        try
        {
            positions = await PositionRepo.GetAllAsync("GetAllPositions");        }
        catch (Exception ex)
        {
            isError = true;
            errorMessage = $"An error occurred while loading positions: {ex.Message}";
        }
    }

    private async Task HandleValidSubmit()
    {
        try
        {
            if (isEditMode)
            {
                // Update Position
                await PositionRepo.UpdateAsync("UpdatePosition", newPosition);
            }
            else
            {
                // Add Position
                await PositionRepo.AddAsync("AddPosition",new{EnglishName=newPosition.EnglishName,DariName = newPosition.DariName,PashtoName = newPosition.PashtoName});
            }

            // Reset after operation
            isSuccess = true;
            isError = false;
            newPosition = new PositionModel();
            isEditMode = false;

            await LoadPositions();
        }
        catch (Exception ex)
        {
            isError = true;
            errorMessage = $"An error occurred: {ex.Message}";
        }
    }

    private void EditPosition(PositionModel position)
    {
        isEditMode = true;
        newPosition = new PositionModel
            {
                PositionId = position.PositionId,
                EnglishName = position.EnglishName,
                DariName = position.DariName,
                PashtoName = position.PashtoName
            };
    }

    private void ConfirmDelete(int positionId)
    {
        // Show confirmation dialog
        positionIdToDelete = positionId;
        showDeleteConfirmation = true;
    }

    private async Task DeletePosition()
    {
        try
        {
            await PositionRepo.DeleteAsync("DeletePosition", new { PositionId  = positionIdToDelete});
            await LoadPositions();
            showDeleteConfirmation = false;  // Close confirmation dialog
            isSuccess = true;
            isError = false;
        }
        catch (Exception ex)
        {
            isError = true;
            errorMessage = $"An error occurred while deleting the position: {ex.Message}";
            showDeleteConfirmation = false;  // Close confirmation dialog
        }
    }

    private void CancelDelete()
    {
        showDeleteConfirmation = false;
    }
}
