﻿using System.ComponentModel.DataAnnotations;

namespace GhalibLanguageAndSkillsCenter.Models.Staff
{
    public class StaffEducationFeild
    {
        public int EducationFeildId { get; set; }

        [Required(ErrorMessage = "English name required.")]
        public string EnglishName { get; set; }

        [Required(ErrorMessage = "Dari name required.")]
        public string DariName { get; set; }

        [Required(ErrorMessage = "Pashto name required.")]
        public string PashtoName { get; set; }
    }
}
