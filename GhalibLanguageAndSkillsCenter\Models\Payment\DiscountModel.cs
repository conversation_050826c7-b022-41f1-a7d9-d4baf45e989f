﻿using System.ComponentModel.DataAnnotations;

namespace GhalibLanguageAndSkillsCenter.Models.Payment
{
    public class DiscountModel
    {
        public int StudentDiscountId { get; set; }
        public decimal DiscountPercentage { get; set; }
        public string Reason { get; set; }
        public int StudentId { get; set; }
        public int ProgramId { get; set; }
        public DateTime Created_At { get; set; }
        public string StudentName { get; set; }
        public string ProgramName { get; set; }
        public int IsActive { get; set; }
    }
}
