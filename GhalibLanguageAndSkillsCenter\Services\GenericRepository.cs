﻿using Dapper;
using GhalibLanguageAndSkillsCenter.Data;
using System.Data;

namespace GhalibLanguageAndSkillsCenter.Services
{
    public class GenericRepository<T> : IGenericRepository<T>
    {
        private readonly DapperContext _context;

        public GenericRepository(DapperContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<T>> GetAllAsync(string storedProcedure)
        {
            using var connection = _context.CreateConnection();
            return await connection.QueryAsync<T>(storedProcedure, commandType: CommandType.StoredProcedure);
        }

        public async Task<IEnumerable<T>> GetAllAsyncById(string storedProcedure,object parameters)
        {
            using var connection = _context.CreateConnection();
            return await connection.QueryAsync<T>(storedProcedure,parameters, commandType: CommandType.StoredProcedure);
        }

        public async Task<T> GetByIdAsync(string storedProcedure, object parameters)
        {
            using var connection = _context.CreateConnection();
            return await connection.QueryFirstOrDefaultAsync<T>(storedProcedure, parameters, commandType: CommandType.StoredProcedure);
        }

        public async Task AddAsync(string storedProcedure, object parameters)
        {
            using var connection = _context.CreateConnection();
            await connection.ExecuteAsync(storedProcedure, parameters, commandType: CommandType.StoredProcedure);
        }

        public async Task UpdateAsync(string storedProcedure, object parameters)
        {
            using var connection = _context.CreateConnection();
            await connection.ExecuteAsync(storedProcedure, parameters, commandType: CommandType.StoredProcedure);
        }

        public async Task DeleteAsync(string storedProcedure, object parameters)
        {
            using var connection = _context.CreateConnection();
            await connection.ExecuteAsync(storedProcedure, parameters, commandType: CommandType.StoredProcedure);
        }

        public async Task<DynamicParameters> AddAsyncAndGetOutpot(string storedProcedure, object parameters)
        {
            // 1) wrap the anonymous object into a DynamicParameters
            var dp = new DynamicParameters(parameters);

            // 2) declare the two OUTPUT params your SP exposes:
            dp.Add("RemainingAmount", dbType: DbType.Decimal, direction: ParameterDirection.Output);
            dp.Add("ReturnCode", dbType: DbType.Int32, direction: ParameterDirection.Output);

            // 3) execute
            using var conn = _context.CreateConnection();
            await conn.ExecuteAsync(
                storedProcedure,
                dp,
                commandType: CommandType.StoredProcedure
            );

            // 4) return the same DynamicParameters (now populated)
            return dp;
        }

    }

}
