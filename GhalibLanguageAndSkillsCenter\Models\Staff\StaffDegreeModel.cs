﻿using System.ComponentModel.DataAnnotations;

namespace GhalibLanguageAndSkillsCenter.Models.Staff
{
    public class StaffDegreeModel
    {
        public int EducationDegreeId { get; set; }

        [Required(ErrorMessage = "English name is required.")]
        public string EnglishName { get; set; }

        [Required(ErrorMessage = "Dari name is required.")]
        public string DariName { get; set; }

        [Required(ErrorMessage = "Pashto name is required.")]
        public string PashtoName { get; set; }
    }
}
