﻿/* === auth.css === */

/* 1. Full-screen animated gradient background */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #6a11cb, #2575fc);
    padding: 2rem;
    background-size: 200% 200%;
    animation: auth-gradientShift 15s ease infinite;
}

@keyframes auth-gradientShift {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

/* 2. Card wrapper */
.auth-card {
    width: 100%;
    max-width: 480px;
    background: rgba(255,255,255,0.85);
    border-radius: 1.2rem;
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    backdrop-filter: blur(12px);
    padding: 2.5rem 2rem;
    border: 1px solid rgba(255,255,255,0.3);
    animation: auth-fadeIn 1s ease-out;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

    .auth-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 14px 28px rgba(0,0,0,0.15);
    }

@keyframes auth-fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 3. Header inside card */
.auth-header {
    text-align: center;
    font-size: 1.9rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: #333;
}

/* 4. Form group spacing */
.auth-form-group {
    margin-bottom: 0.75rem;
}

.auth-form-group--checkbox {
    margin-bottom: 1.5rem;
}

/* 5. Inputs and selects */
.auth-input,
.auth-select {
    width: 100%;
    border-radius: 0.8rem;
    padding: 0.9rem 1rem;
    font-size: 1rem;
    border: 1px solid #ddd;
    background: white;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

    .auth-input:focus,
    .auth-select:focus {
        border-color: #2575fc;
        box-shadow: 0 0 5px rgba(37,117,252,0.5);
    }

.auth-select {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,\
%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%236C757D'%3e\
%3cpath d='M7 10l5 5 5-5z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 1rem;
}

/* 6. Validation messages */
.auth-validation {
    color: #e50000;
    font-size: 0.875rem;
    margin-top: 0.25rem;
    margin-bottom: 0.5rem;
}

/* 7. Submit button */
.auth-btn {
    width: 100%;
    padding: 0.75rem;
    font-size: 1.05rem;
    font-weight: 500;
    color: #fff;
    background: #2575fc;
    border: none;
    border-radius: 1rem;
    transition: background 0.3s ease, transform 0.2s ease;
}

    .auth-btn:hover {
        background: #6a11cb;
        transform: translateY(-2px);
    }

/* 8. Links under form */
.auth-links {
    text-align: center;
    margin-top: 1rem;
    font-size: 0.95rem;
}

    .auth-links a {
        color: #2575fc;
        text-decoration: none;
        font-weight: 500;
        margin: 0 0.5rem;
        transition: text-decoration 0.2s ease, opacity 0.2s ease;
    }

        .auth-links a:hover {
            text-decoration: underline;
            opacity: 0.85;
        }

/* 9. Error boundary */
.blazor-error-boundary {
    background: #b32121 url(data:image/svg+xml;base64,PHN2Zy... ) no-repeat 1rem/1.8rem;
    color: white;
    padding: 1rem 1rem 1rem 3.7rem;
}

    .blazor-error-boundary::after {
        content: "An error has occurred.";
    }

/* Responsive tweaks */
@media (max-width: 576px) {
    .auth-card {
        padding: 1.5rem 1rem;
    }

    .auth-header {
        font-size: 1.6rem;
    }
}
